# Application Configuration
NODE_ENV=development
PORT=3000
API_PREFIX=api/v1

# Database Configuration (Prisma)
DATABASE_URL=mongodb://localhost:27017/herond-notifications?replicaSet=rs0

# Message Queue Configuration
RABBITMQ_URL=amqp://localhost:5672

# Firebase Cloud Messaging (FCM) Configuration
FCM_PROJECT_ID=your-project-id
FCM_PRIVATE_KEY=your-private-key
FCM_CLIENT_EMAIL=your-client-email

# Apple Push Notification Service (APNS) Configuration
APNS_KEY_ID=your-key-id
APNS_TEAM_ID=your-team-id
APNS_PRIVATE_KEY=your-private-key
APNS_BUNDLE_ID=your-bundle-id
APNS_PRODUCTION=false

# Email Configuration (SMTP)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your-account-sid
TWILIO_AUTH_TOKEN=your-auth-token
TWILIO_FROM_NUMBER=+**********

# Windows Notification Service (WNS) Configuration
WNS_CLIENT_ID=your-client-id
WNS_CLIENT_SECRET=your-client-secret

# Security Configuration
JWT_SECRET=your-jwt-secret
JWT_EXPIRES_IN=24h

# Rate Limiting
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_INTERVAL=30000

# Retry Configuration
MAX_RETRY_ATTEMPTS=3
RETRY_DELAY_MS=5000
EXPONENTIAL_BACKOFF=true
