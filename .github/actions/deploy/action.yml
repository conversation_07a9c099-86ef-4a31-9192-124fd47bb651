name: 'Deploy Service'
description: 'Bump tag, build, and push a Docker image'

inputs:
  service:
    description: 'Service name'
    required: true
  environment:
    description: 'Deployment environment'
    required: true
  update_level:
    description: 'Version bump level (major/minor/patch)'
    required: true
  docker-user:
    description: 'Docker username'
    required: true
  docker-password:
    description: 'Docker password'
    required: true
  github_token:
    description: 'GitHub token'
    required: true
  lark_webhook:
    description: 'Lark webhook'
    required: false
  registry-name:
    description: 'ACR container registry name'
    required: true
  repository:
    description: 'ACR container repository'
    required: true
    default: herond-notification-center
  docker-file-path:
    description: Path to docker file
    required: true
    default: ./Dockerfile
runs:
  using: 'composite'
  steps:
    - name: Bump tag
      id: bump
      uses: actions/github-script@v8
      with:
        github-token: ${{ github.token }}
        script: |
          const {bumpTag} = require('./.github/scripts/bump-tag.js');
          const result = await bumpTag({
            github,
            context,
            service: "${{ inputs.service }}",
            env: "${{ inputs.environment }}",
            updateLevel: "${{ inputs.update_level }}"
          });
          core.setOutput("latestTag", result.latestTag);
          core.setOutput("newTag", result.newTag);

    - name: Set up QEMU
      uses: docker/setup-qemu-action@v2

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    - name: Login to Azure Containter Registry
      uses: docker/login-action@v2
      with:
        registry: ${{ inputs.registry-name }}
        username: ${{ inputs.docker-user }}
        password: ${{ inputs.docker-password }}

    - name: Build and push Docker images
      id: docker_build
      uses: docker/build-push-action@v4.0.0
      with:
        context: .
        file: ${{ inputs.docker-file-path }}
        push: true
        build-args: environment=${{ inputs.environment }}
        tags: ${{ inputs.registry-name }}/${{ inputs.repository }}:${{ steps.bump.outputs.newTag }}

    - name: Add summary
      run: |
        echo "### Deployment Result" >> $GITHUB_STEP_SUMMARY
        echo "- **Service**: ${{ inputs.service }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Environment**: ${{ inputs.environment }}" >> $GITHUB_STEP_SUMMARY
        echo "- **New Tag**: \`${{ steps.bump.outputs.newTag }}\`" >> $GITHUB_STEP_SUMMARY
      shell: bash

    - name: Determine msg color
      if: always()
      shell: bash
      run: |
        if [[ "${{ steps.docker_build.outcome }}" == "success" ]]; then
          echo "MSG_COLOR=green" >> $GITHUB_ENV
        else
          echo "MSG_COLOR=red" >> $GITHUB_ENV
        fi

    - name: Notify Lark
      if: always()
      uses: herondlabs/action-lark-notify@main
      with:
        columnsPerRow: 2
        token: ${{ inputs.github_token }}
        cardItems: |
          **Repo**<br>[${{ github.repository }}](${{ github.server_url }}/${{ github.repository }})
          **Actor**<br>${{ github.actor }}
          **Branch**<br>[${{ github.ref_name }}](${{ github.server_url }}/${{ github.repository }}/tree/${{ github.ref_name }})
          **Image Tag**<br>{{NEW_TAG}}
          **Build Logs**<br>[{{GITHUB_RUN_ID}}]({{GITHUB_JOB_URL}})
      env:
        LARK_WEBHOOK: ${{ inputs.lark_webhook }}
        NEW_TAG: ${{ steps.bump.outputs.newTag }}
        LARK_MESSAGE_SUBTITLE: |
          Status: ${{ job.status }}
        LARK_MESSAGE_TEMPLATE: '${{ env.MSG_COLOR }}'
        LARK_MESSAGE_TITLE: 'Herond Notification Center Build'
