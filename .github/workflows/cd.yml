name: Build and Push to Registry
run-name: >
  Deployment to Env: ${{ github.event.inputs.environment }},
  Level: ${{ github.event.inputs.update_level }}
on:
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        options: [dev, staging, prod]
      update_level:
        type: choice
        options: [major, minor, patch]
        default: patch
      notification-service:
        type: boolean
        default: true
      fcm-service:
        type: boolean
        default: false
permissions:
  contents: write
  actions: read
jobs:
  notification-service:
    if: ${{ github.event.inputs.notification-service == 'true' }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
      - name: Bump tag, build, and push a Docker image
        uses: ./.github/actions/deploy
        with:
          service: notification
          environment: ${{ github.event.inputs.environment }}
          update_level: ${{ github.event.inputs.update_level }}
          docker-user: ${{ secrets.HEROND_PRODUCTION_ACR_USERNAME }}
          docker-password: ${{ secrets.HEROND_PRODUCTION_ACR_PASSWORD }}
          lark_webhook: ${{ secrets.LARK_WEBHOOK }}
          github_token: ${{ secrets.GITHUB_TOKEN }}
          registry-name: ${{ secrets.HEROND_PRODUCTION_ACR_USERNAME }}.azurecr.io
          repository: ${{ vars.REPOSITORY_NAME }}
          docker-file-path: './apps/notification-service/Dockerfile'
  fcm-service:
    if: ${{ github.event.inputs.fcm-service == 'true' }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
      - name: Bump tag, build, and push a Docker image
        uses: ./.github/actions/deploy
        with:
          service: fcm
          environment: ${{ github.event.inputs.environment }}
          update_level: ${{ github.event.inputs.update_level }}
          docker-user: ${{ secrets.HEROND_PRODUCTION_ACR_USERNAME }}
          docker-password: ${{ secrets.HEROND_PRODUCTION_ACR_PASSWORD }}
          lark_webhook: ${{ secrets.LARK_WEBHOOK }}
          github_token: ${{ secrets.GITHUB_TOKEN }}
          registry-name: ${{ secrets.HEROND_PRODUCTION_ACR_USERNAME }}.azurecr.io
          repository: ${{ vars.REPOSITORY_NAME }}
          docker-file-path: './apps/fcm-service/Dockerfile'
