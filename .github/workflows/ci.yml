name: CI

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  build:
    runs-on: ubuntu-latest

    services:
      mongodb:
        image: mongo:7.0
        ports:
          - 27017:27017
        options: >-
          --health-cmd "mongosh --eval 'db.runCommand({ping: 1})'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      rabbitmq:
        image: rabbitmq:3.12-management-alpine
        ports:
          - 5672:5672
        env:
          RABBITMQ_DEFAULT_USER: admin
          RABBITMQ_DEFAULT_PASS: admin123
        options: >-
          --health-cmd "rabbitmq-diagnostics -q ping"
          --health-interval 30s
          --health-timeout 30s
          --health-retries 3

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Use Node.js 22.x
        uses: actions/setup-node@v4
        with:
          node-version: 22.x
          cache: 'npm'

      - name: Install deps, generate Prisma, lint, test, and build
        run: |
          npm ci --legacy-peer-deps
          make prisma-generate
          npm run lint
          npm run format:check
          npm run test --if-present
          npm run test:e2e --if-present
          npm run build
        env:
          MONGODB_URI: mongodb://localhost:27017/herond-notifications-test
          RABBITMQ_URL: amqp://admin:admin123@localhost:5672

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-files
          path: dist/
