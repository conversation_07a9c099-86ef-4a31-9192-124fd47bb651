{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Debug consumers-service with Nx", "runtimeExecutable": "npx", "runtimeArgs": ["nx", "serve", "consumers-service"], "env": {"NODE_OPTIONS": "--inspect=9229"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "sourceMaps": true, "outFiles": ["${workspaceFolder}/consumers-service/dist/**/*.(m|c|)js", "!**/node_modules/**"]}, {"type": "node", "request": "launch", "name": "Debug fcm-service with Nx", "runtimeExecutable": "npx", "runtimeArgs": ["nx", "serve", "fcm-service"], "env": {"NODE_OPTIONS": "--inspect=9230"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "sourceMaps": true, "outFiles": ["${workspaceFolder}/fcm-service/dist/**/*.(m|c|)js", "!**/node_modules/**"]}, {"type": "node", "request": "launch", "name": "Debug sms-service with Nx", "runtimeExecutable": "npx", "runtimeArgs": ["nx", "serve", "sms-service"], "env": {"NODE_OPTIONS": "--inspect=9231"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "sourceMaps": true, "outFiles": ["${workspaceFolder}/apps/sms-service/dist/**/*.(m|c|)js", "!**/node_modules/**"]}, {"type": "node", "request": "launch", "name": "Debug email-service with Nx", "runtimeExecutable": "npx", "runtimeArgs": ["nx", "serve", "email-service"], "env": {"NODE_OPTIONS": "--inspect=9232"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "sourceMaps": true, "outFiles": ["${workspaceFolder}/apps/email-service/dist/**/*.(m|c|)js", "!**/node_modules/**"]}, {"type": "node", "request": "launch", "name": "Debug apns-service with Nx", "runtimeExecutable": "npx", "runtimeArgs": ["nx", "serve", "apns-service"], "env": {"NODE_OPTIONS": "--inspect=9233"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "sourceMaps": true, "outFiles": ["${workspaceFolder}/apps/apns-service/dist/**/*.(m|c|)js", "!**/node_modules/**"]}, {"type": "node", "request": "launch", "name": "Debug wns-service with Nx", "runtimeExecutable": "npx", "runtimeArgs": ["nx", "serve", "wns-service"], "env": {"NODE_OPTIONS": "--inspect=9234"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "skipFiles": ["<node_internals>/**"], "sourceMaps": true, "outFiles": ["${workspaceFolder}/apps/wns-service/dist/**/*.(m|c|)js", "!**/node_modules/**"]}]}