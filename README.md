# Herond Notification Center

A scalable, multi-platform notification center solution built with NestJS, supporting FCM, APNS, Email, SMS, and WNS channels.

## 🚀 Features

- **Multi-Platform Support**: FCM, APNS, Email, SMS, WNS
- **Scalable Architecture**: Built with NestJS and NX monorepo
- **Message Queue**: RabbitMQ for reliable message processing
- **Database**: MongoDB with Prisma ORM for data persistence
- **Real-time Processing**: Asynchronous notification delivery
- **Template System**: Reusable notification templates
- **Rate Limiting**: Advanced rate limiting with Redis storage
- **Health Monitoring**: Built-in health checks for all providers
- **Docker Support**: Containerized for easy deployment
- **CI/CD Ready**: GitHub Actions workflows included
- **Comprehensive Testing**: Unit and integration tests
- **API Documentation**: Swagger/OpenAPI documentation

## 🏗️ Architecture

```mermaid
flowchart LR
    API[API Gateway] --> Notification[Notification Service]
    Notification --> RabbitMQ[Rabbit MQ]
    RabbitMQ --> FCM[FCM Provider]
    RabbitMQ --> APNS[APNS Provider]
    RabbitMQ --> Email[Email Provider]
    RabbitMQ --> SMS[SMS Provider]
    RabbitMQ --> WNS[WNS Provider]
    Notification --> Database[Database]
```

## 📋 Prerequisites

- Node.js 22+
- Docker and Docker Compose
- MongoDB 7.0+
- RabbitMQ 3.12+

## 🛠️ Installation

### Using Docker (Recommended)

1. Clone the repository:

```bash
git clone https://github.com/your-org/herond-notification-center.git
cd herond-notification-center
```

2. Copy environment variables:

```bash
cp .env.example .env
```

3. Start the services:

```bash
docker-compose up -d
```

### Manual Installation

1. Install dependencies:

```bash
npm install --legacy-peer-deps
```

2. Set up environment variables:

```bash
cp .env.example .env
# Edit .env with your configuration
```

3. Start MongoDB and RabbitMQ services

4. Generate Prisma client and push schema:

```bash
npm run db:generate
npm run db:push
```

5. Run the application:

```bash
npm run start:dev
```

## 🔧 Configuration

### Environment Variables

| Variable       | Description                        | Default                                          |
| -------------- | ---------------------------------- | ------------------------------------------------ |
| `NODE_ENV`     | Environment                        | `development`                                    |
| `PORT`         | Application port                   | `3000`                                           |
| `DATABASE_URL` | MongoDB connection string (Prisma) | `mongodb://localhost:27017/herond-notifications` |
| `RABBITMQ_URL` | RabbitMQ connection string         | `amqp://localhost:5672`                          |

### Provider Configuration

#### Firebase Cloud Messaging (FCM)

```env
FCM_PROJECT_ID=your-project-id
FCM_PRIVATE_KEY=your-private-key
FCM_CLIENT_EMAIL=your-client-email
```

#### Apple Push Notification Service (APNS)

```env
APNS_KEY_ID=your-key-id
APNS_TEAM_ID=your-team-id
APNS_PRIVATE_KEY=your-private-key
APNS_BUNDLE_ID=your-bundle-id
APNS_PRODUCTION=false
```

#### Email (SMTP)

```env
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>
```

#### SMS (Twilio)

```env
TWILIO_ACCOUNT_SID=your-account-sid
TWILIO_AUTH_TOKEN=your-auth-token
TWILIO_FROM_NUMBER=+**********
```

#### Windows Notification Service (WNS)

```env
WNS_CLIENT_ID=your-client-id
WNS_CLIENT_SECRET=your-client-secret
```

## 🗄️ Database Management

This project uses Prisma as the ORM for MongoDB. Here are the key commands:

### Generate Prisma Client

```bash
npm run db:generate
```

### Push Schema to Database

```bash
npm run db:push
```

### Open Prisma Studio (Database GUI)

```bash
npm run db:studio
```

### Run Database Migrations (for SQL databases)

```bash
npm run db:migrate
```

### Seed Database

```bash
npm run db:seed
```

## 📚 API Usage

### Send Notification

```bash
curl -X POST http://localhost:3000/api/v1/notifications \
  -H "Content-Type: application/json" \
  -d '{
    "payload": {
      "title": "Hello World",
      "body": "This is a test notification"
    },
    "recipients": [
      {
        "id": "user-123",
        "channel": "fcm",
        "address": "fcm-device-token"
      }
    ],
    "channels": ["fcm"],
    "priority": "normal"
  }'
```

### Create Template

```bash
curl -X POST http://localhost:3000/api/v1/templates \
  -H "Content-Type: application/json" \
  -d '{
    "name": "welcome-notification",
    "description": "Welcome notification for new users",
    "channels": ["fcm", "email"],
    "payload": {
      "title": "Welcome {{username}}!",
      "body": "Thank you for joining our platform"
    },
    "variables": ["username"]
  }'
```

## 🧪 Testing

Run unit tests:

```bash
npm run test
```

Run e2e tests:

```bash
npm run test:e2e
```

Run tests with coverage:

```bash
npm run test:cov
```

## 🚀 Deployment

### Production Deployment

1. Build the application:

```bash
npm run build
```

2. Start with Docker Compose:

```bash
docker-compose -f docker-compose.prod.yml up -d
```

### Environment-specific Deployments

- **Staging**: Automatically deployed on push to `main` branch
- **Production**: Deployed on git tag creation (e.g., `v1.0.0`)

## 🛡️ Rate Limiting

The API implements comprehensive rate limiting to prevent abuse:

- **Send Notification**: 10 requests/minute (20 for authenticated users)
- **Register Device**: 5 requests/5 minutes (10 for authenticated users)
- **User Preferences**: 20 requests/minute (40 for authenticated users)
- **General API**: 100 requests/minute (200 for authenticated users)

Rate limit headers are included in all responses:

- `X-RateLimit-Limit`: Maximum requests allowed
- `X-RateLimit-Remaining`: Remaining requests in current window
- `X-RateLimit-Reset`: When the rate limit resets

For detailed information, see [Rate Limiting Documentation](docs/RATE_LIMITING.md).

## 📊 Monitoring

- **Health Checks**: `/health` endpoint
- **Metrics**: Prometheus metrics available at `/metrics`
- **API Documentation**: Available at `/api/v1/docs`
- **Rate Limiting**: Redis-based distributed rate limiting

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'feat: add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
