{"name": "apns-service-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["apns-service"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "apps/apns-service-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["apns-service:build", "apns-service:serve"]}}}