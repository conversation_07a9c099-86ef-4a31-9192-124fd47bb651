{"name": "apns-service", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/apns-service/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "dependsOn": ["libs:build"], "options": {"command": "webpack-cli build", "args": ["--node-env=production"], "cwd": "apps/apns-service"}, "configurations": {"development": {"args": ["--node-env=development"]}}}, "prune-lockfile": {"dependsOn": ["build"], "cache": true, "executor": "@nx/js:prune-lockfile", "outputs": ["{workspaceRoot}/dist/apps/apns-service/package.json", "{workspaceRoot}/dist/apps/apns-service/package-lock.json"], "options": {"buildTarget": "build"}}, "copy-workspace-modules": {"dependsOn": ["build"], "cache": true, "outputs": ["{workspaceRoot}/dist/apps/apns-service/workspace_modules"], "executor": "@nx/js:copy-workspace-modules", "options": {"buildTarget": "build"}}, "prune": {"dependsOn": ["prune-lockfile", "copy-workspace-modules"], "executor": "nx:noop"}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "apns-service:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "apns-service:build:development"}, "production": {"buildTarget": "apns-service:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}