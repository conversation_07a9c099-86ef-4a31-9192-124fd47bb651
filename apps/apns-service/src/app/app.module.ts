import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { AppService } from './app.service';
import { NotificationModule } from '@libs';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.development', '.env'],
    }),
    NotificationModule,
  ],
  controllers: [],
  providers: [AppService],
})
export class AppModule {}
