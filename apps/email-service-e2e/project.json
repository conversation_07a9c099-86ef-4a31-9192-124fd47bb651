{"name": "email-service-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["email-service"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "apps/email-service-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["email-service:build", "email-service:serve"]}}}