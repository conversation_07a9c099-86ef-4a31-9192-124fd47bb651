{"name": "fcm-service-e2e", "$schema": "../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["fcm-service"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "fcm-service-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["fcm-service:build", "fcm-service:serve"]}}}