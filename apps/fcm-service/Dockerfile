FROM node:24-alpine AS base
RUN apk add --no-cache libc6-compat
WORKDIR /app


FROM base AS deps
COPY package*.json ./
RUN npm ci --only=production --ignore-scripts && npm cache clean --force


FROM base AS builder
COPY package*.json ./
RUN npm ci --ignore-scripts

COPY nx.json tsconfig.base.json ./
COPY apps/fcm-service ./apps/fcm-service
COPY libs ./libs

COPY prisma ./prisma
COPY prisma.config.ts ./
RUN npx prisma generate

RUN npx nx build fcm-service

FROM base AS runner
WORKDIR /app
ENV NODE_ENV=production
 
RUN addgroup --system --gid 1001 nodejs \
  && adduser --system --uid 1001 nestjs

COPY --from=deps --chown=nestjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nestjs:nodejs /app/dist/apps/fcm-service ./dist
COPY --from=builder --chown=nestjs:nodejs /app/package*.json ./
COPY --from=builder --chown=nodeuser:nodejs /app/prisma/generated ./prisma/generated

USER nestjs
EXPOSE 3000
CMD ["node", "dist/main.js"]
