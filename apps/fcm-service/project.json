{"name": "fcm-service", "$schema": "../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/fcm-service/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "nx:run-commands", "dependsOn": ["libs:build"], "options": {"command": "webpack-cli build", "args": ["--node-env=production"], "cwd": "apps/fcm-service"}, "configurations": {"development": {"args": ["--node-env=development"]}}}, "prune-lockfile": {"dependsOn": ["build"], "cache": true, "executor": "@nx/js:prune-lockfile", "outputs": ["{workspaceRoot}/dist/fcm-service/package.json", "{workspaceRoot}/dist/fcm-service/package-lock.json"], "options": {"buildTarget": "build"}}, "copy-workspace-modules": {"dependsOn": ["build"], "cache": true, "outputs": ["{workspaceRoot}/dist/fcm-service/workspace_modules"], "executor": "@nx/js:copy-workspace-modules", "options": {"buildTarget": "build"}}, "prune": {"dependsOn": ["prune-lockfile", "copy-workspace-modules"], "executor": "nx:noop"}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "fcm-service:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "fcm-service:build:development"}, "production": {"buildTarget": "fcm-service:build:production"}}}, "test": {"options": {"passWithNoTests": true}}}}