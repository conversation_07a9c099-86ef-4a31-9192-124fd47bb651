import { DatabaseModule, FcmProvider, INotificationProvider, NotificationService } from '@libs';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { FcmNotificationLowConsumer } from './consumers/fcm-notification-low.consumer';
import { FcmNotificationHighConsumer } from './consumers/fcm-notification-high.consumer';
import { FcmNotificationNormalConsumer } from './consumers/fcm-notification-normal.consumer';
import { FcmNotificationCriticalConsumer } from './consumers/fcm-notification-critical.consumer';
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.development', '.env'],
    }),
    DatabaseModule,
  ],
  providers: [{ provide: INotificationProvider, useClass: FcmProvider }, NotificationService],
  controllers: [
    FcmNotificationLowConsumer,
    FcmNotificationNormalConsumer,
    FcmNotificationHighConsumer,
    FcmNotificationCriticalConsumer,
  ],
})
export class AppModule {}
