import { <PERSON>, Logger } from '@nestjs/common';
import { EventPattern, Payload } from '@nestjs/microservices';
import { FcmMessage, type NotificationQueueMessage, NotificationService } from '@libs';

@Controller()
export class FcmNotificationCriticalConsumer {
  private readonly logger = new Logger(FcmNotificationCriticalConsumer.name);

  constructor(private readonly notificationService: NotificationService) {}

  @EventPattern('notification.fcm.critical')
  async handle(@Payload() message: NotificationQueueMessage) {
    this.logger.log(`Processing FCM notification: ${message.id}`);
    const result = await this.notificationService.sendNotification(new FcmMessage(message));
  }
}
