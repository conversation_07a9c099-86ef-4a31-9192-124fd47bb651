import { <PERSON>, Logger } from '@nestjs/common';
import { EventPattern, Payload } from '@nestjs/microservices';
import { FcmMessage, NotificationService } from '@libs';
@Controller()
export class FcmNotificationHighConsumer {
  private readonly logger = new Logger(FcmNotificationHighConsumer.name);

  constructor(private readonly notificationService: NotificationService) {}

  @EventPattern('notification.fcm.high')
  async handle(@Payload() message: any) {
    this.logger.log(`Processing FCM notification: ${message.id}`);
    const result = await this.notificationService.sendNotification(new FcmMessage(message));
  }
}
