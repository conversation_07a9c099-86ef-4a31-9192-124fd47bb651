import { FcmMessage, NotificationService } from '@libs';
import { <PERSON>, Logger } from '@nestjs/common';
import { EventPattern, Payload } from '@nestjs/microservices';

@Controller()
export class FcmNotificationLowConsumer {
  private readonly logger = new Logger(FcmNotificationLowConsumer.name);

  constructor(
    private readonly notificationService: NotificationService // private readonly notificationRepository: NotificationRepository, // private readonly notificationResultRepository: NotificationResultRepository
  ) {}

  async onModuleInit() {
    this.logger.log('FCM Consumer Service initialized');
  }

  @EventPattern('notification.fcm.low')
  async handle(@Payload() message: any) {
    this.logger.log(`Processing FCM notification: ${message.id}`);
    const result = await this.notificationService.sendNotification(new FcmMessage(message));
  }
}
