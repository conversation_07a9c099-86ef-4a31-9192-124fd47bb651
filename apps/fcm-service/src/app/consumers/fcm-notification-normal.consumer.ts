import { <PERSON>, Logger } from '@nestjs/common';
import { EventPattern, Payload } from '@nestjs/microservices';
import { NotificationService, type NotificationQueueMessage, FcmMessage } from '@libs';

@Controller()
export class FcmNotificationNormalConsumer {
  private readonly logger = new Logger(FcmNotificationNormalConsumer.name);

  constructor(private readonly notificationService: NotificationService) {}

  @EventPattern('notification.fcm.normal')
  async handle(@Payload() message: NotificationQueueMessage) {
    this.logger.log(`Processing FCM notification: ${message.id}`);
    const result = await this.notificationService.sendNotification(new FcmMessage(message));
    //TODO: handle failed results send to dead letter queue
  }
}
