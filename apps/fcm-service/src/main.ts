import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';

import { subscribeQueue } from '@libs';
import { AppModule } from './app/app.module';

async function bootstrap() {
  const logger = new Logger('FcmService');

  const app = await NestFactory.create(AppModule);

  await subscribeQueue(app, {
    queue: 'notifications.fcm',
    routingKey: 'notification.fcm',
  });
  // Also start HTTP server for health checks
  const port = process.env.PORT || 3001;
  await app.listen(port);

  logger.log(`🚀 Fcm Service is running on: http://localhost:${port}`);
}

bootstrap().catch(error => {
  Logger.error('Failed to start Fcm Service', error);
  process.exit(1);
});
