# Application Configuration
NODE_ENV=development
PORT=3000

API_VERSION=0.0.1

# Database Configuration (Prisma)
DATABASE_URL=mongodb+srv://herondlabdevs:<EMAIL>/herond-notifications-localhost?retryWrites=true&w=majority&appName=Cluster0

# Message Queue Configuration
RABBITMQ_URL=amqp://admin:admin@123@localhost:5672

# Rate Limiting Configuration
# Global rate limiting (fallback for all endpoints)
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=5


# Redis configuration for distributed rate limiting
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_KEY_PREFIX=rate-limit: