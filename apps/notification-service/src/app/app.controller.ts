import { Controller, Get } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Controller()
export class AppController {
  constructor(private readonly configService: ConfigService) {}

  @Get('/version')
  getData() {
    return {
      NotificationApiVersion: {
        Version: this.configService.get('API_VERSION'),
        BuildTime: this.configService.get('BUILD_TIME'),
      },
    };
  }
}
