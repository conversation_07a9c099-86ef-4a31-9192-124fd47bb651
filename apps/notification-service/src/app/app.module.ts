import { DatabaseModule, HostModule, QueueModule, RateLimitingModule } from '@libs';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { NotificationsController } from './controllers/notifications.controller';
import { NotificationService } from './services/notification.service';
import { HealthController } from './controllers/health.controller';
import { TerminusModule } from '@nestjs/terminus';
import { AuthModule } from '../infrastructure/auth/auth.module';
import { DeviceService } from './services/device.service';
import { DeviceController } from './controllers/device.controller';
import { UserPreferencesService } from './services/user-preferences.service';
import { UserPreferencesController } from './controllers/user-preferences.controller';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.development', '.env'],
    }),
    QueueModule,
    DatabaseModule,
    TerminusModule,
    HostModule,
    AuthModule,
    RateLimitingModule,
  ],
  controllers: [
    AppController,
    NotificationsController,
    HealthController,
    DeviceController,
    UserPreferencesController,
  ],
  providers: [NotificationService, DeviceService, UserPreferencesService],
})
export class AppModule {}
