import { Body, Controller, Delete, HttpCode, Param, Patch, UseGuards } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { DeviceService } from '../services/device.service';
import { RefreshDeviceTokenRequest } from '../dto/devices/refresh-device-token.request';
import { AuthGuard } from '@nestjs/passport';
import { AuthzGuard, Scopes } from '../../infrastructure';

@ApiTags('Devices')
@Controller('devices')
export class DeviceController {
  constructor(private readonly deviceService: DeviceService) {}

  @Patch(':id/refresh-device-token')
  @UseGuards(AuthGuard('jwt'), AuthzGuard)
  @Scopes('firebase.messaging')
  @HttpCode(204)
  @ApiOperation({
    summary: 'Refresh device registration token',
    description:
      'Updates the registration token (FCM/APNs) for a device. Typically used when a mobile app refreshes the token periodically (e.g., monthly job).',
  })
  @ApiParam({
    name: 'id',
    description: 'Unique identifier of the device (ObjectId in MongoDB)',
    example: '651fa79c2a22e442d4a1f314',
  })
  @ApiBody({ type: RefreshDeviceTokenRequest })
  @ApiResponse({
    status: 204,
    description: 'Device registration token successfully refreshed (No Content).',
  })
  @ApiResponse({
    status: 404,
    description: 'Device not found.',
  })
  async refreshDeviceToken(
    @Param('id') id: string,
    @Body() dto: RefreshDeviceTokenRequest
  ): Promise<void> {
    await this.deviceService.refreshDeviceToken(id, dto);
  }

  @Delete(':id')
  @UseGuards(AuthGuard('jwt'), AuthzGuard)
  @Scopes('firebase.messaging')
  @HttpCode(204)
  @ApiOperation({
    summary: 'Delete device',
    description:
      'Removes a device registration. Typically used when a user logs out, clears data, or reinstalls the app.',
  })
  @ApiParam({
    name: 'id',
    description: 'Unique identifier of the device (ObjectId in MongoDB)',
    example: '651fa79c2a22e442d4a1f314',
  })
  @ApiResponse({
    status: 204,
    description: 'Device successfully deleted (No Content).',
  })
  @ApiResponse({
    status: 404,
    description: 'Device not found.',
  })
  async deleteDevice(@Param('id') id: string): Promise<void> {
    await this.deviceService.deleteDevice(id);
  }
}
