import { Body, Controller, Logger, Post, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { NotificationService } from '../services/notification.service';
import { RegisterDeviceDto } from '../dto/register-device.dto';
import { SendNotificationRequestDto } from '../dto/send-notification.dto';
import { AuthGuard } from '@nestjs/passport';
import { AuthzGuard, Scopes } from '../../infrastructure';

@ApiTags('Notifications')
@Controller('notifications')
export class NotificationsController {
  private readonly logger = new Logger(NotificationsController.name);
  constructor(private readonly notificationService: NotificationService) {}

  @Post('register-device')
  @UseGuards(AuthGuard('jwt'), AuthzGuard)
  @Scopes('firebase.messaging')
  async registerDevice(@Body() registerDeviceDto: RegisterDeviceDto) {
    this.logger.log('Registering device');
    const device = await this.notificationService.registerDevice(registerDeviceDto);
    return device;
  }

  @Post('send')
  @UseGuards(AuthGuard('jwt'), AuthzGuard)
  @Scopes('notifications')
  async sendNotification(@Body() sendNotificationRequestDto: SendNotificationRequestDto) {
    this.logger.log('Sending notification');
    await this.notificationService.sendNotification(sendNotificationRequestDto);
  }
}
