import { Body, Controller, Logger, Post, UseGuards } from '@nestjs/common';
import { ApiTags, ApiResponse, ApiHeader } from '@nestjs/swagger';
import { NotificationService } from '../services/notification.service';
import { RegisterDeviceDto } from '../dto/register-device.dto';
import { SendNotificationRequestDto } from '../dto/send-notification.dto';
import { AuthGuard } from '@nestjs/passport';
import { AuthzGuard, Scopes } from '../../infrastructure';

@ApiTags('Notifications')
@Controller('notifications')
export class NotificationsController {
  private readonly logger = new Logger(NotificationsController.name);
  constructor(private readonly notificationService: NotificationService) {}

  @Post('register-device')
  @UseGuards(AuthGuard('jwt'), AuthzGuard)
  @Scopes('firebase.messaging')
  @ApiResponse({
    status: 201,
    description: 'Device registered successfully',
  })
  @ApiResponse({
    status: 429,
    description: 'Rate limit exceeded. Maximum 5 registrations per 5 minutes.',
    headers: {
      'X-RateLimit-Limit': {
        description: 'Request limit per time window',
        schema: { type: 'integer' },
      },
      'X-RateLimit-Remaining': {
        description: 'Remaining requests in current window',
        schema: { type: 'integer' },
      },
      'X-RateLimit-Reset': {
        description: 'Time when the rate limit resets (Unix timestamp)',
        schema: { type: 'integer' },
      },
    },
  })
  async registerDevice(@Body() registerDeviceDto: RegisterDeviceDto) {
    this.logger.log('Registering device');
    const device = await this.notificationService.registerDevice(registerDeviceDto);
    return device;
  }

  @Post('send')
  @UseGuards(AuthGuard('jwt'), AuthzGuard)
  @Scopes('notifications')
  @ApiResponse({
    status: 200,
    description: 'Notification sent successfully',
  })
  @ApiResponse({
    status: 429,
    description: 'Rate limit exceeded. Maximum 10 notifications per minute.',
    headers: {
      'X-RateLimit-Limit': {
        description: 'Request limit per time window',
        schema: { type: 'integer' },
      },
      'X-RateLimit-Remaining': {
        description: 'Remaining requests in current window',
        schema: { type: 'integer' },
      },
      'X-RateLimit-Reset': {
        description: 'Time when the rate limit resets (Unix timestamp)',
        schema: { type: 'integer' },
      },
    },
  })
  async sendNotification(@Body() sendNotificationRequestDto: SendNotificationRequestDto) {
    this.logger.log('Sending notification');
    await this.notificationService.sendNotification(sendNotificationRequestDto);
  }
}
