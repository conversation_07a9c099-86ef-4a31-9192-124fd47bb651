import { Body, Controller, Get, Param, Post, Query, UseGuards } from '@nestjs/common';
import { UserPreferencesService } from '../services/user-preferences.service';
import { UserPreferencesResponse } from '../dto/user-preferences/get-user-preferences.response';
import { CreateUserPreferencesRequest } from '../dto/user-preferences/create-user-preferences.request';
import type { GetUserPreferencesRequest } from '../dto/user-preferences/get-user-preferences.request';
import { ApiBody, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { AuthzGuard, Scopes } from '../../infrastructure';

@ApiTags('User Preferences')
@Controller('user-preferences')
export class UserPreferencesController {
  constructor(private readonly service: UserPreferencesService) {}

  @Post()
  @UseGuards(AuthGuard('jwt'), AuthzGuard)
  @Scopes('firebase.messaging')
  @ApiOperation({ summary: 'Create or update user preferences' })
  @ApiBody({ type: CreateUserPreferencesRequest })
  @ApiResponse({
    status: 201,
    description: 'Preferences created or updated successfully',
    type: UserPreferencesResponse,
  })
  async upsert(@Body() dto: CreateUserPreferencesRequest) {
    return this.service.upsert(dto);
  }

  @Get(':id')
  @UseGuards(AuthGuard('jwt'), AuthzGuard)
  @Scopes('firebase.messaging')
  @ApiOperation({ summary: 'Get user preferences by ID' })
  @ApiParam({ name: 'id', description: 'Unique identifier of the user preference' })
  @ApiResponse({
    status: 200,
    description: 'User preference found',
    type: UserPreferencesResponse,
  })
  @ApiResponse({ status: 404, description: 'User preference not found' })
  async getById(@Param('id') id: string): Promise<UserPreferencesResponse> {
    return this.service.getById(id);
  }

  @Get()
  @UseGuards(AuthGuard('jwt'), AuthzGuard)
  @Scopes('firebase.messaging')
  @ApiOperation({ summary: 'Find user preferences by scopes' })
  @ApiQuery({ name: 'userId', required: true, description: 'ID of the user' })
  @ApiQuery({ name: 'appCode', required: true, description: 'Application code' })
  @ApiResponse({
    status: 200,
    description: 'Matching user preferences',
    type: UserPreferencesResponse,
  })
  async find(@Query() query: GetUserPreferencesRequest) {
    return this.service.findByUserAndScopes(query);
  }
}
