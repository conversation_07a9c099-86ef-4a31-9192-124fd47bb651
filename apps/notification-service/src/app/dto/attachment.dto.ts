import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsUrl } from 'class-validator';

export class AttachmentDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The file name of the attachment.',
    example: 'invoice.pdf',
  })
  filename!: string;

  @IsUrl()
  @ApiProperty({
    description: 'The publicly accessible URL of the attachment file.',
    example: 'https://cdn.example.com/files/invoice.pdf',
  })
  url!: string;
}
