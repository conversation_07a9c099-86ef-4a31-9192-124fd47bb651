import { ApiPropertyOptional } from '@nestjs/swagger';
import { <PERSON>Array, IsEmail, IsOptional, IsString } from 'class-validator';

export class DestinationDto {
  @IsString()
  @ApiPropertyOptional({
    description: 'The unique identifier of the user who should receive the notification.',
    example: 'user_12345',
  })
  userId: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ApiPropertyOptional({
    description: 'The device token used for push notifications (e.g., FCM or APNs token).',
    example: ['fcm_device_token_123', 'apns_device_token_456'],
  })
  deviceTokens?: string[];

  @IsOptional()
  @IsArray()
  @IsEmail({}, { each: true })
  @ApiPropertyOptional({
    description: 'Recipients of the notification (email addresses for email channel).',
    example: ['<EMAIL>', '<EMAIL>'],
  })
  toEmails?: string[];

  @IsOptional()
  @IsArray()
  @IsEmail({}, { each: true })
  @ApiPropertyOptional({
    description: 'Carbon copy recipients (CC).',
    example: ['<EMAIL>'],
  })
  ccEmails?: string[];

  @IsOptional()
  @IsArray()
  @IsEmail({}, { each: true })
  @ApiPropertyOptional({
    description: 'Blind carbon copy recipients (BCC).',
    example: ['<EMAIL>'],
  })
  bccEmails?: string[];
}
