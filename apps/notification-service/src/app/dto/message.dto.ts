import { EmailProviderType, type FcmPayload } from '@libs';
import { ApiExtraModels, ApiProperty, ApiPropertyOptional, getSchemaPath } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsOptional, IsString, ValidateNested } from 'class-validator';
import { AttachmentDto } from './attachment.dto';

export class EmailMessageDto {
  @ApiProperty({
    enum: EmailProviderType,
    description: 'Email provider to use for sending notification',
    example: EmailProviderType.SesSmtp,
  })
  provider: EmailProviderType;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: 'The subject of the message (mainly for email).',
    example: 'Welcome Email',
  })
  subject: string;

  @IsOptional()
  @IsString()
  @ApiPropertyOptional({
    description: 'The main content of the message.',
    example: '<p>Hello John, welcome to our platform!</p>',
  })
  content: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AttachmentDto)
  @ApiPropertyOptional({
    description: 'List of attachments to be included in the message.',
    type: () => AttachmentDto,
    isArray: true,
    example: [
      {
        filename: 'invoice.pdf',
        url: 'https://cdn.example.com/files/invoice.pdf',
      },
    ],
  })
  attachments?: AttachmentDto[];
}

@ApiExtraModels()
export class MessageDto {
  @ApiPropertyOptional({
    description: 'The push notification message for FCM (Firebase MulticastMessage).',
    oneOf: [{ $ref: getSchemaPath(Object) }],
    example: {
      subject: 'Notification Title',
      content: 'Notification Content {{content}}',
      data: {},
      android: {
        priority: 'high',
        notification: {
          channelId: 'orders',
          sound: 'ping.mp3',
        },
      },
      apns: {
        payload: {
          aps: {
            badge: 1,
            sound: 'default',
            interruptionLevel: 'time-sensitive',
          },
        },
      },
    },
  })
  @IsOptional()
  fcm?: FcmPayload;

  @ApiPropertyOptional({
    description: 'The email message payload.',
    type: () => EmailMessageDto,
  })
  @IsOptional()
  email?: EmailMessageDto;

  @IsOptional()
  @ApiPropertyOptional({
    description:
      'Dynamic key-value pairs used for template rendering or additional metadata. ' +
      'For example, placeholders in the template such as {{username}} or {{amount}} ' +
      'will be replaced with the values provided here.',
    example: {
      username: 'Alice',
      amount: '100 ETH',
      socketId: 'abc123',
    },
    type: 'object',
    additionalProperties: true,
  })
  data?: Record<string, any>;
}
