import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsArray,
  IsObject,
  IsEnum,
  IsOptional,
  IsNumber,
  IsDate,
  ValidateNested,
  ArrayMinSize,
  IsNotEmpty,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { NotificationChannel, NotificationPriority, NotificationStatus } from '@libs';

export class NotificationRecipientDto {
  @ApiProperty({ description: 'Recipient ID' })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({ enum: NotificationChannel, description: 'Notification channel' })
  @IsEnum(NotificationChannel)
  channel: NotificationChannel;

  @ApiProperty({ description: 'Recipient address (email, phone, token, etc.)' })
  @IsString()
  @IsNotEmpty()
  address: string;

  @ApiPropertyOptional({ description: 'Additional recipient metadata' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class NotificationPayloadDto {
  @ApiProperty({ description: 'Notification title' })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({ description: 'Notification body/message' })
  @IsString()
  @IsNotEmpty()
  body: string;

  @ApiPropertyOptional({ description: 'Additional data payload' })
  @IsOptional()
  @IsObject()
  data?: Record<string, any>;

  @ApiPropertyOptional({ description: 'HTML content for email notifications' })
  @IsOptional()
  @IsString()
  html?: string;

  @ApiPropertyOptional({ description: 'Badge count for push notifications' })
  @IsOptional()
  @IsNumber()
  badge?: number;

  @ApiPropertyOptional({ description: 'Sound for push notifications' })
  @IsOptional()
  @IsString()
  sound?: string;

  @ApiPropertyOptional({ description: 'Email attachments' })
  @IsOptional()
  @IsArray()
  attachments?: any[];

  @ApiPropertyOptional({ description: 'Sender information' })
  @IsOptional()
  @IsString()
  from?: string;

  @ApiPropertyOptional({ description: 'Notification type (for WNS)' })
  @IsOptional()
  @IsString()
  type?: string;
}

export class CreateNotificationDto {
  @ApiPropertyOptional({ description: 'Template ID to use' })
  @IsOptional()
  @IsString()
  templateId?: string;

  @ApiProperty({ type: NotificationPayloadDto, description: 'Notification payload' })
  @ValidateNested()
  @Type(() => NotificationPayloadDto)
  payload: NotificationPayloadDto;

  @ApiProperty({ type: [NotificationRecipientDto], description: 'List of recipients' })
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => NotificationRecipientDto)
  recipients: NotificationRecipientDto[];

  @ApiProperty({ enum: NotificationChannel, isArray: true, description: 'Notification channels' })
  @IsArray()
  @IsEnum(NotificationChannel, { each: true })
  channels: NotificationChannel[];

  @ApiPropertyOptional({
    enum: NotificationPriority,
    description: 'Notification priority',
    default: NotificationPriority.Normal,
  })
  @IsOptional()
  @IsEnum(NotificationPriority)
  priority?: NotificationPriority;

  @ApiPropertyOptional({ description: 'Schedule notification for later' })
  @IsOptional()
  @IsDate()
  @Transform(({ value }) => new Date(value))
  scheduledAt?: Date;

  @ApiPropertyOptional({ description: 'Additional metadata' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Created by user/system' })
  @IsOptional()
  @IsString()
  createdBy?: string;
}

export class NotificationResultDto {
  @ApiProperty({ description: 'Recipient ID' })
  recipientId: string;

  @ApiProperty({ enum: NotificationChannel, description: 'Notification channel' })
  channel: NotificationChannel;

  @ApiProperty({ enum: NotificationStatus, description: 'Result status' })
  status: NotificationStatus;

  @ApiPropertyOptional({ description: 'Sent timestamp' })
  sentAt?: Date;

  @ApiPropertyOptional({ description: 'Delivered timestamp' })
  deliveredAt?: Date;

  @ApiPropertyOptional({ description: 'Error message if failed' })
  error?: string;
}

export class NotificationResponseDto {
  @ApiProperty({ description: 'Notification ID' })
  id: string;

  @ApiProperty({ enum: NotificationStatus, description: 'Notification status' })
  status: NotificationStatus;

  @ApiProperty({ description: 'Response message' })
  message: string;

  @ApiProperty({ description: 'Queued timestamp' })
  queuedAt: Date;

  @ApiPropertyOptional({ description: 'Sent timestamp' })
  sentAt?: Date;

  @ApiProperty({ description: 'Number of recipients' })
  recipients: number;

  @ApiProperty({ enum: NotificationChannel, isArray: true, description: 'Notification channels' })
  channels: NotificationChannel[];

  @ApiPropertyOptional({ type: [NotificationResultDto], description: 'Delivery results' })
  results?: NotificationResultDto[];
}

export class NotificationListItemDto {
  @ApiProperty({ description: 'Notification ID' })
  id: string;

  @ApiProperty({ enum: NotificationStatus, description: 'Notification status' })
  status: NotificationStatus;

  @ApiProperty({ description: 'Number of recipients' })
  recipients: number;

  @ApiProperty({ enum: NotificationChannel, isArray: true, description: 'Notification channels' })
  channels: NotificationChannel[];

  @ApiProperty({ description: 'Created timestamp' })
  createdAt: Date;

  @ApiPropertyOptional({ description: 'Sent timestamp' })
  sentAt?: Date;
}

export class PaginationDto {
  @ApiProperty({ description: 'Current page number' })
  page: number;

  @ApiProperty({ description: 'Items per page' })
  limit: number;

  @ApiProperty({ description: 'Total number of items' })
  total: number;

  @ApiProperty({ description: 'Total number of pages' })
  pages: number;
}

export class NotificationListResponseDto {
  @ApiProperty({ type: [NotificationListItemDto], description: 'List of notifications' })
  data: NotificationListItemDto[];

  @ApiProperty({ type: PaginationDto, description: 'Pagination information' })
  pagination: PaginationDto;
}
