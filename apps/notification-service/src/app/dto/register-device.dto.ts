import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsEnum, IsOptional } from 'class-validator';
import { NotificationChannel } from '@libs';

export class RegisterDeviceDto {
  @ApiProperty({ description: 'Device token for push notifications' })
  @IsString()
  @IsNotEmpty()
  deviceToken: string;

  @ApiProperty({
    description: 'Device platform/channel',
    enum: NotificationChannel,
    example: NotificationChannel.Fcm,
  })
  @IsEnum(NotificationChannel)
  platform: NotificationChannel;

  @ApiProperty({ description: 'User ID associated with the device', required: false })
  @IsString()
  @IsOptional()
  userId: string;

  @ApiProperty({ description: 'Application code/identifier', required: false })
  @IsString()
  @IsOptional()
  appCode: string;

  @ApiProperty({ description: 'Operating system version', required: false })
  @IsString()
  @IsOptional()
  osVersion?: string;

  @ApiProperty({ description: 'Application version', required: false })
  @IsString()
  @IsOptional()
  appVersion?: string;
}
