import {
  ArrayNotEmpty,
  IsArray,
  IsIn,
  IsLocale,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { NotificationChannel, NotificationPriority, NotificationCategory } from '@libs';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { DestinationDto } from './destination.dto';
import { MessageDto } from './message.dto';

export class SendNotificationRequestDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: 'Application code (herond_wallet...)', required: false })
  appCode!: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ description: 'User ID of the sender', required: false })
  authorId!: string;

  @IsArray()
  @ArrayNotEmpty()
  @IsIn(Object.values(NotificationChannel), { each: true })
  @ApiProperty({
    description: 'The list of primary channels to deliver the notification.',
    enum: NotificationChannel,
    isArray: true,
    example: [NotificationChannel.Fcm, NotificationChannel.Email],
  })
  channels!: NotificationChannel[];

  @IsOptional()
  @IsArray()
  @IsIn(Object.values(NotificationChannel), { each: true })
  @ApiPropertyOptional({
    description: 'Fallback channels to be used if delivery through primary channels fails.',
    enum: NotificationChannel,
    isArray: true,
    example: [NotificationChannel.Sms],
  })
  fallbackChannels?: NotificationChannel[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DestinationDto)
  @ApiProperty({
    description: 'List of destinations to deliver the notification.',
    type: DestinationDto,
    isArray: true,
  })
  destinations!: DestinationDto[];

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The unique code that identifies the event triggering this notification.',
    example: 'USER_TRANSFER_ETH',
  })
  eventCode!: string;

  @IsString()
  @IsOptional()
  @ApiProperty({
    description: 'The category used to classify the notification.',
    example: NotificationCategory.Transactional,
  })
  categoryCode!: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    description: 'The code of the template used to render the notification.',
    example: 'USER_OTP',
  })
  templateCode!: string;

  @IsOptional()
  @IsLocale()
  @ApiPropertyOptional({
    description: 'Locale for the notification template (ISO 639 language code).',
    example: 'en-US',
  })
  locale?: string;

  @ValidateNested()
  @Type(() => MessageDto)
  @ApiProperty({
    description: 'The message content including subject, body, and attachments.',
    type: () => MessageDto,
  })
  message!: MessageDto;

  @IsOptional()
  @IsIn(Object.values(NotificationPriority), { each: true })
  @ApiPropertyOptional({
    description: 'The priority level of the notification (e.g., normal, high, urgent).',
    enum: NotificationPriority,
    example: NotificationPriority.High,
  })
  priority?: NotificationPriority;

  @IsOptional()
  @ApiPropertyOptional({
    description:
      'Optional schedule for notification delivery. Accepts either: \n' +
      '- **ISO 8601 datetime string** for one-time scheduling (e.g., `"2025-08-20T12:00:00Z"`). \n' +
      '- **Cron expression** for recurring jobs (5-field format: minute hour day month day-of-week, e.g., `"0 12 20 8 *"` → every Aug 20 at 12:00). \n' +
      'If omitted, the notification is sent immediately.',
    examples: {
      oneTime: { value: '2025-08-20T12:00:00Z' },
      recurring: { value: '0 12 20 8 *' },
    },
  })
  scheduleTime?: string;
}
