import { Type } from 'class-transformer';
import { IsString, IsBoolean, IsArray, ValidateNested, IsEnum, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { NotificationChannel } from '@libs';

export class CreateUserPreferencesRequest {
  @ApiProperty({
    example: '68da396e307b88cea14c97dc',
    description: 'Unique identifier of the preferences (optional for create)',
    required: false,
  })
  @IsString()
  @IsOptional()
  id?: string;

  @ApiProperty({ example: 'user_12345', description: 'ID of the user' })
  @IsString()
  @IsOptional()
  userId?: string;

  @ApiProperty({
    example: true,
    description: 'Flag to allow or block all notifications',
  })
  @IsBoolean()
  allowNotifications: boolean;

  @ApiProperty({
    type: () => [ScopeDto],
    description: 'List of scopes that define where preferences apply (e.g., wallet, device)',
    required: false,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ScopeDto)
  scopes: ScopeDto[];

  @ApiProperty({
    type: () => [OptionDto],
    description: 'List of notification options by category',
    required: false,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OptionDto)
  options: OptionDto[];

  @ApiProperty({ example: 'herond_wallet', description: 'Application code' })
  @IsString()
  appCode: string;
}

export class OptionDto {
  @ApiProperty({
    example: 'Trade',
    description: 'Notification category (e.g., Trade, Security, System)',
  })
  @IsString()
  category: string;

  @ApiProperty({
    enum: NotificationChannel,
    isArray: true,
    required: false,
    description: 'Allowed channels for this option',
    example: undefined,
  })
  @IsArray()
  @IsEnum(NotificationChannel, { each: true })
  @IsOptional()
  channels: NotificationChannel[];

  @ApiProperty({
    example: true,
    description: 'Flag to enable/disable this option',
  })
  @IsBoolean()
  enabled: boolean;
}

export class ScopeDto {
  @ApiProperty({
    example: 'wallet',
    description: 'Scope field (e.g., wallet, device, chain)',
  })
  @IsString()
  field: string;

  @ApiProperty({
    example: 'wallet_abcdef',
    description: 'Scope value (e.g., walletId, deviceId)',
  })
  @IsString()
  value: string;
}
