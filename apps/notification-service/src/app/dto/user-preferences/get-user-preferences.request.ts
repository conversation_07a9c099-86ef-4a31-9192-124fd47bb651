import { Type } from 'class-transformer';
import { IsArray, IsNotEmpty, IsOptional, IsString, ValidateNested } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ScopeQuery {
  @ApiProperty({
    example: 'wallet',
    description: 'Scope field (e.g., wallet, device, chain)',
  })
  @IsString()
  @IsNotEmpty()
  field: string;

  @ApiProperty({
    example: 'wallet_abcdef',
    description: 'Scope value (e.g., walletId, deviceId)',
  })
  @IsString()
  @IsNotEmpty()
  value: string;
}

export class GetUserPreferencesRequest {
  @ApiProperty({
    example: 'user_12345',
    description: 'ID of the user',
  })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  userId?: string;

  @ApiPropertyOptional({
    type: [ScopeQuery],
    description: 'Optional list of scopes to filter preferences (e.g., wallet, device, chain)',
  })
  @ValidateNested({ each: true })
  @Type(() => ScopeQuery)
  @IsOptional()
  @IsArray()
  scopes?: ScopeQuery[];

  @ApiProperty({
    example: 'herond_wallet',
    description: 'Application code to filter preferences',
  })
  @IsString()
  @IsNotEmpty()
  appCode: string;
}
