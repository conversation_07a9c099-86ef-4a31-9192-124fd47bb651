import { NotificationChannel, type UserPreferences } from '@libs';

export class OptionResponse {
  category: string;
  channels: NotificationChannel[] = [];
  enabled: boolean;
}

export class ScopeResponse {
  field: string;
  value: string;
}

export class UserPreferencesResponse {
  id: string;
  userId: string;
  appCode: string;
  allowNotifications: boolean;
  scopes: ScopeResponse[];
  options: OptionResponse[];
  updatedAt: Date;

  static mapToUserPreferencesResponse(model: UserPreferences): UserPreferencesResponse {
    return {
      id: model.id,
      userId: model.userId,
      appCode: model.appCode,
      allowNotifications: model.allowNotifications,
      updatedAt: model.updatedAt,
      scopes: (model.scopes ?? []).map(
        (s): ScopeResponse => ({
          field: s.field,
          value: s.value,
        })
      ),
      options: (model.options ?? []).map(
        (o): OptionResponse => ({
          category: o.category,
          channels: o.channels ? [...o.channels] : [],
          enabled: o.enabled,
        })
      ),
    };
  }
}
