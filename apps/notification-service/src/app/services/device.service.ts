import { Injectable, NotFoundException } from '@nestjs/common';
import { DeviceRepository } from '@libs';
import type { RefreshDeviceTokenRequest } from '../dto/devices/refresh-device-token.request';
import { IdentityService } from '../../infrastructure';

@Injectable()
export class DeviceService {
  constructor(
    private readonly deviceRepository: DeviceRepository,
    private readonly identityService: IdentityService
  ) {}

  async refreshDeviceToken(id: string, dto: RefreshDeviceTokenRequest): Promise<void> {
    const device = await this.deviceRepository.findBy({
      id: id,
      deviceToken: dto.deviceToken,
      userId: this.identityService.getUserId(),
    });

    if (!device) {
      throw new NotFoundException(`Device with id ${id} and token ${dto.deviceToken} not found`);
    }

    await this.deviceRepository.refreshDeviceToken(id, dto.deviceToken);
  }

  async deleteDevice(id: string): Promise<void> {
    const userId = this.identityService.getUserId();
    const device = await this.deviceRepository.findBy({
      id: id,
      userId: this.identityService.getUserId(),
    });

    if (!device) {
      throw new NotFoundException(`Device with id ${id} not found for user ${userId}`);
    }

    const result = await this.deviceRepository.deleteById(id, userId);
    if (result.count === 0) {
      throw new NotFoundException(`Device with id ${id} not found`);
    }
  }
}
