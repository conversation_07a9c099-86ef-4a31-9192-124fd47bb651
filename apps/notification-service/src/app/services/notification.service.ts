import {
  DeviceRepository,
  NotificationChannel,
  NotificationSnapshotRepository,
  NotificationStatus,
  QueueRouterService,
  type NotificationQueueMessage,
  type NotificationRecipient,
} from '@libs';
import { Injectable, Logger } from '@nestjs/common';
import { ObjectId } from 'bson';
import type { RegisterDeviceDto } from '../dto/register-device.dto';
import type { SendNotificationRequestDto } from '../dto/send-notification.dto';
import type { NotificationMessageSnapshot, NotificationPayload } from '@libs';
import type { MessageDto } from '../dto/message.dto';
import { IdentityService } from '../../infrastructure';
import { UserPreferencesService } from './user-preferences.service';

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);
  constructor(
    private readonly deviceRepository: DeviceRepository,
    private readonly notificationSnapshotRepository: NotificationSnapshotRepository,
    private readonly queueRouterService: QueueRouterService,
    private readonly identityService: IdentityService,
    private readonly userPreferencesService: UserPreferencesService
  ) {}

  async registerDevice(registerDeviceDto: RegisterDeviceDto) {
    this.logger.log(`Registering device for user: ${registerDeviceDto.userId}`);
    const device = await this.deviceRepository.registerDevice({
      id: new ObjectId().toString(),
      userId: registerDeviceDto.userId ?? this.identityService.getUserId(),
      appCode: registerDeviceDto.appCode,
      deviceToken: registerDeviceDto.deviceToken,
      platform: registerDeviceDto.platform,
      osVersion: registerDeviceDto.osVersion,
      appVersion: registerDeviceDto.appVersion,
      tokenRefreshedAt: new Date(),
    });

    return device;
  }

  /**
   * Send a notification by queuing it to appropriate channels
   */
  async sendNotification(sendNotificationRequestDto: SendNotificationRequestDto) {
    this.logger.log('Creating new notification');

    //TODO: Implement scheduled notification
    const now = new Date();
    const recipients = await this.getRecipientsByDestinations(sendNotificationRequestDto);
    if (!recipients || recipients.length == 0) {
      return;
    }

    const queuedMessages = this.getQueuedMessages(
      sendNotificationRequestDto.channels,
      recipients,
      sendNotificationRequestDto
    );

    await this.queueRouterService.routeNotifications(queuedMessages);

    const snapshotMessages: NotificationMessageSnapshot[] = queuedMessages.map(x => {
      return {
        ...x,
        status: NotificationStatus.Pending,
        retryCount: 0,
        maxRetries: 3,
        createdAt: now,
      };
    });

    await this.notificationSnapshotRepository.createMessagesSnapshot(snapshotMessages);
  }

  private getQueuedMessages(
    channels: NotificationChannel[],
    recipients: NotificationRecipient[],
    dto: SendNotificationRequestDto
  ): NotificationQueueMessage[] {
    const {
      templateCode,
      authorId,
      fallbackChannels,
      priority,
      message,
      categoryCode,
      eventCode,
      appCode,
    } = dto;

    const result: NotificationQueueMessage[] = [];

    for (const channel of channels) {
      for (const recipient of recipients) {
        result.push({
          id: new ObjectId().toString(),
          channel,
          recipient,
          templateCode,
          authorId,
          fallbackChannels,
          priority,
          payload: this.getMessages(message, channel),
          categoryCode,
          eventCode,
          appCode,
        });
      }
    }
    return result;
  }

  private getMessages(messageDto: MessageDto, channel: NotificationChannel): NotificationPayload {
    return {
      ...messageDto[channel.toLowerCase()],
      templateData: messageDto.data,
    };
  }

  private getRecipients(recipients: NotificationRecipient[]): NotificationRecipient[] {
    const groupedRecipients = recipients.reduce((prev, curr) => {
      if (prev[curr.id]) {
        prev[curr.id].push(curr);
      } else {
        prev[curr.id] = [curr];
      }
      return prev;
    }, {} as Record<string, NotificationRecipient[]>);
    return Object.entries(groupedRecipients).map(([key, value]) => {
      return {
        id: key,
        address: value.map(x => x.address).flat(),
      };
    });
  }

  private async getRecipientsByDestinations(
    sendNotificationRequestDto: SendNotificationRequestDto
  ) {
    const destinations = sendNotificationRequestDto.destinations;
    const appCode = sendNotificationRequestDto.appCode;
    const data = sendNotificationRequestDto.message.data;

    const specificDeviceTokens = destinations.filter(x => x.deviceTokens);
    const specificEmails = destinations.filter(x => x.toEmails || x.ccEmails);

    const usersId = destinations
      .filter(x => !x.deviceTokens && !x.toEmails && !x.ccEmails)
      .map(x => x.userId);

    const usersDestinations = await this.deviceRepository.getUserTokenDevices(usersId, appCode);

    //TODO: Implement email, sms, wns
    const allRecipients: NotificationRecipient[] = [
      ...specificDeviceTokens.map(x => ({ address: x.deviceTokens, id: x.userId })),
      ...usersDestinations.map(x => ({ address: x.deviceToken, id: x.userId })),
      ...specificEmails.map(x => ({
        address: { to: x.toEmails, cc: x.ccEmails, bcc: x.bccEmails },
        id: x.userId,
      })),
    ];

    const recipients: NotificationRecipient[] = [];
    for (const r of allRecipients) {
      if (
        await this.userPreferencesService.checkSendNotification({
          userId: r.id,
          appCode,
          data: data ?? {},
          categoryCode: sendNotificationRequestDto.categoryCode,
        })
      ) {
        recipients.push(r);
      }
    }

    return this.getRecipients(recipients);
  }
}
