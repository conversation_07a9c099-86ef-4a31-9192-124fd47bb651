import { Injectable, NotFoundException } from '@nestjs/common';
import type { CreateUserPreferencesRequest } from '../dto/user-preferences/create-user-preferences.request';
import { UserPreferencesRepository } from '@libs';
import type { UserPreferences } from '@libs';
import type { GetUserPreferencesRequest } from '../dto/user-preferences/get-user-preferences.request';
import { UserPreferencesResponse } from '../dto/user-preferences/get-user-preferences.response';
import type { CheckSendNotificationRequest } from '../dto/user-preferences/check-send-notification.request';
import { IdentityService } from '../../infrastructure';

@Injectable()
export class UserPreferencesService {
  constructor(
    private readonly repo: UserPreferencesRepository,
    private readonly identityService: IdentityService
  ) {}

  async upsert(dto: CreateUserPreferencesRequest) {
    const data: UserPreferences = {
      id: dto.id,
      userId: dto.userId ?? this.identityService.getUserId(),
      appCode: dto.appCode,
      allowNotifications: dto.allowNotifications ?? true,
      scopes: dto.scopes ?? [],
      options: dto.options ?? [],
      updatedAt: new Date(),
    };

    if (dto.id) {
      return this.repo.upsert(data);
    }
    return this.repo.create(data);
  }

  async findByUserAndScopes(dto: GetUserPreferencesRequest) {
    const where: any = {
      userId: dto.userId ?? this.identityService.getUserId(),
      appCode: dto.appCode,
    };

    if (dto.scopes && dto.scopes.length > 0) {
      where.scopes = {
        some: {
          OR: dto.scopes.map(s => ({
            field: s.field,
            value: s.value,
          })),
        },
      };
    }

    const result = await this.repo.readPrismaService.userPreferences.findFirst({
      where,
    });

    if (!result) {
      throw new NotFoundException(
        `UserPreferences for userId=${dto.userId} and appCode=${dto.appCode} not found`
      );
    }

    return UserPreferencesResponse.mapToUserPreferencesResponse(result as UserPreferences);
  }

  async getById(id: string): Promise<UserPreferencesResponse> {
    const model = await this.repo.readPrismaService.userPreferences.findUnique({
      where: { id },
    });

    if (!model) {
      throw new NotFoundException(`UserPreferences with id ${id} not found`);
    }

    return UserPreferencesResponse.mapToUserPreferencesResponse(model as UserPreferences);
  }

  async checkSendNotification(request: CheckSendNotificationRequest): Promise<boolean> {
    const where: any = {
      userId: request.userId,
      appCode: request.appCode,
    };

    const userPreferencesList = await this.repo.readPrismaService.userPreferences.findMany({
      where,
    });

    if (!userPreferencesList || userPreferencesList.length === 0) {
      return true;
    }

    let matchedPreferences = userPreferencesList.find(prefs =>
      (prefs.scopes ?? []).every(scope => request.data[scope.field] === scope.value)
    );

    if (!matchedPreferences) {
      matchedPreferences = userPreferencesList[0];
    }

    if (!matchedPreferences.allowNotifications) {
      return false;
    }

    if (matchedPreferences.scopes && matchedPreferences.scopes.length > 0) {
      const allScopesMatch = matchedPreferences.scopes.every(
        scope => request.data[scope.field] === scope.value
      );
      if (!allScopesMatch) {
        return false;
      }
    }

    if (matchedPreferences.options && matchedPreferences.options.length > 0) {
      const hasMatchingOption = matchedPreferences.options.some(option => {
        if (!option.enabled) return false;

        const notificationType = request.data['notificationType']?.toString();
        const directCategory = request.categoryCode?.toString();

        return (
          notificationType === option.category.toString() ||
          directCategory === option.category.toString()
        );
      });

      if (!hasMatchingOption) {
        return false;
      }
    }

    return true;
  }
}
