import { Environment } from '@libs';

const authConfig = {
  dev: {
    issuers: {
      'https://herond.org': 'https://accounts-dev.herond.org/.well-known/openid-configuration/jwks',
    },
  },
  uat: {
    issuers: {
      'https://herond.org': 'https://accounts-uat.herond.org/.well-known/openid-configuration/jwks',
    },
  },
  prod: {
    issuers: {
      'https://herond.org': 'https://accounts.herond.org/.well-known/openid-configuration/jwks',
    },
  },
  externals: {
    issuers: {
      'https://accounts.google.com': 'https://www.googleapis.com/oauth2/v3/certs',
      'https://login.microsoftonline.com/common/v2.0':
        'https://login.microsoftonline.com/common/discovery/v2.0/keys',
    },
  },
};

export function getIssuerMap() {
  let envIssuers = {};

  if (Environment.isDev) {
    envIssuers = authConfig.dev.issuers;
  } else if (Environment.isStaging) {
    envIssuers = authConfig.uat.issuers;
  } else if (Environment.isProd) {
    envIssuers = authConfig.prod.issuers;
  }

  return {
    ...authConfig.externals.issuers,
    ...envIssuers,
  };
}
