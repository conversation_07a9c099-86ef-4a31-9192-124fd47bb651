import {
  Injectable,
  ForbiddenException,
  type ExecutionContext,
  type CanActivate,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';

@Injectable()
export class AuthzGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    const requiredScopes = this.reflector.get<string[]>('scopes', context.getHandler());

    if (!requiredScopes) return true;

    const tokenScopes = user.scopes || [];
    const ok = requiredScopes.every(s => tokenScopes.includes(s));
    if (!ok) {
      throw new ForbiddenException(`Missing required scopes: ${requiredScopes.join(', ')}`);
    }

    return true;
  }
}
