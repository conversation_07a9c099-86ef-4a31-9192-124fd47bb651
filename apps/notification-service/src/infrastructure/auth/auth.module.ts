import { Modu<PERSON> } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { JwtStrategy } from './jwt.strategy';
import { AuthzGuard } from './auth.guard';
import { ClsModule } from 'nestjs-cls';
import type { AuthenticatedRequest } from './types';
import { IdentityService } from './identity.service';

@Module({
  imports: [
    PassportModule,
    ClsModule.forRoot({
      global: true,
      interceptor: {
        mount: true,
        setup(cls, context) {
          const req = context.switchToHttp().getRequest<AuthenticatedRequest>();
          if (req) {
            cls.set('IDENTITY', req.user);
          }
        },
      },
    }),
  ],
  providers: [JwtStrategy, AuthzGuard, IdentityService],
  exports: [JwtStrategy, AuthzGuard, IdentityService],
})
export class AuthModule {}
