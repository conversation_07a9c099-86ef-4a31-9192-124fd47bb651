import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ClsService } from 'nestjs-cls';
import type { IdentityContext } from './types';

@Injectable()
export class IdentityService {
  constructor(private readonly cls: ClsService) {}

  private get identity(): IdentityContext | null {
    return this.cls.get('IDENTITY') ?? null;
  }

  getIdentity(): IdentityContext {
    const identity = this.identity;
    if (!identity) {
      throw new UnauthorizedException('No identity in context');
    }
    return identity;
  }

  getUserId(): string {
    const identity = this.getIdentity();
    if (identity.type !== 'user' || !identity.userId) {
      throw new UnauthorizedException('Current identity is not a user');
    }
    return identity.userId;
  }

  getEmail(): string {
    const identity = this.getIdentity();
    if (identity.type !== 'user' || !identity.email) {
      throw new UnauthorizedException('Email not available for current identity');
    }
    return identity.email;
  }

  getClientId(): string {
    const identity = this.getIdentity();
    if (identity.type !== 'service' || !identity.clientId) {
      throw new UnauthorizedException('Current identity is not a service');
    }
    return identity.clientId;
  }

  getScopes(): string[] {
    return this.getIdentity().scopes;
  }

  getIssuer(): string {
    return this.getIdentity().issuer;
  }
}
