import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy, ExtractJwt } from 'passport-jwt';
import jwksRsa from 'jwks-rsa';
import { getIssuerMap } from './auth.config';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, 'jwt') {
  constructor() {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      algorithms: ['RS256'],
      secretOrKeyProvider: (req, rawJwt, done) => {
        try {
          const header = JSON.parse(Buffer.from(rawJwt.split('.')[0], 'base64').toString('utf8'));

          const kid = header.kid;
          const issuer = JSON.parse(
            Buffer.from(rawJwt.split('.')[1], 'base64').toString('utf8')
          ).iss;

          const issuerMap = getIssuerMap();
          const jwksUri = issuerMap[issuer];

          if (!jwksUri) {
            return done(new UnauthorizedException(`Unsupported issuer: ${issuer}`), null);
          }

          const client = jwksRsa({
            jwksUri,
            cache: true,
            rateLimit: true,
          });

          client.getSigningKey(kid, (err, key) => {
            console.log('err', err);
            if (err) return done(err, null);
            const signingKey = key.getPublicKey();
            done(null, signingKey);
          });
        } catch (e) {
          done(e, null);
        }
      },
    });
  }

  async validate(payload: any) {
    if (payload.client_id && payload.sub == null) {
      return {
        type: 'service',
        clientId: payload.client_id,
        scopes: (payload.scope || '').split(' '),
        issuer: payload.iss,
      };
    }

    if (payload.sub) {
      return {
        type: 'user',
        userId: payload.sub,
        email: payload.email,
        scopes: (payload.scope || '').split(' '),
        issuer: payload.iss,
      };
    }

    throw new UnauthorizedException('Invalid token payload');
  }
}
