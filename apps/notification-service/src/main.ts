import { Logger, ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';

import { AppModule } from './app/app.module';
import { Environment } from '@libs';
import type { CustomOrigin } from '@nestjs/common/interfaces/external/cors-options.interface';
import type { NestExpressApplication } from '@nestjs/platform-express';

const whitelist = process.env.APP_ALLOW_ORIGINS?.split(',') || ['https://wallet-ui.herond.org'];

const corsOptions = {
  origin: function (
    origin: string | undefined,
    callback: (err: Error | null, origin?: boolean | string) => void
  ): void {
    if (Environment.isDev) {
      callback(null, true);
    } else {
      if (!origin || whitelist.includes(origin)) {
        callback(null, true);
      } else {
        console.warn(`CORS blocked: ${origin}`);
        callback(new Error('Not allowed by CORS'));
      }
    }
  } as CustomOrigin,
  credentials: true,
};

async function bootstrap() {
  Environment.init();

  const app = await NestFactory.create<NestExpressApplication>(AppModule);
  app.set('query parser', 'extended');

  const configService = app.get(ConfigService);

  // Global prefix
  const globalPrefix = configService.get('API_PREFIX', 'api/v1');
  app.setGlobalPrefix(globalPrefix);

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    })
  );

  // CORS configuration
  app.enableCors(corsOptions);

  // Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('Herond Notification Center API')
    .setDescription(
      'A scalable notification center solution supporting multiple platforms and channels'
    )
    .setVersion('1.0')
    .addBearerAuth()
    .build();

  app.use(`${globalPrefix}/docs`, (req, res, next) => {
    res.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.set('Pragma', 'no-cache');
    res.set('Expires', '0');
    next();
  });

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup(`${globalPrefix}/docs`, app, document);

  const port = configService.get('PORT', 3000);
  await app.listen(port);

  Logger.log(`🚀 Application is running on: http://localhost:${port}/${globalPrefix}`);
  Logger.log(`📚 API Documentation: http://localhost:${port}/${globalPrefix}/docs`);
}

bootstrap();
