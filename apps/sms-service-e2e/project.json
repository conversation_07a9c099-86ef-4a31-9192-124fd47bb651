{"name": "sms-service-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["sms-service"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "apps/sms-service-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["sms-service:build", "sms-service:serve"]}}}