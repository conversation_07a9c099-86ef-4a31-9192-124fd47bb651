import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { Transport, type MicroserviceOptions } from '@nestjs/microservices';
import { ConfigService } from '@nestjs/config';

import { AppModule } from './app/app.module';

async function bootstrap() {
  const logger = new Logger('WnsService');

  // Create microservice for each queue

  // Create hybrid application (HTTP + Microservice)
  const app = await NestFactory.create(AppModule);

  const configService = app.get(ConfigService);
  const rabbitmqUrl = configService.get<string>('RABBITMQ_URL', 'amqp://localhost:5672');

  // Connect to WNS queue
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.RMQ,
    options: {
      urls: [rabbitmqUrl],
      queue: 'notifications.wns',
      queueOptions: { durable: true },
      prefetchCount: 10,
    },
  });

  // Start all microservices
  await app.startAllMicroservices();

  // Also start HTTP server for health checks
  const port = process.env.PORT || 3001;
  await app.listen(port);

  logger.log(`🚀 Wns Service is running on: http://localhost:${port}`);
  logger.log(`📡 Connected to RabbitMQ: ${rabbitmqUrl}`);
}

bootstrap().catch(error => {
  Logger.error('Failed to start Wns Service', error);
  process.exit(1);
});
