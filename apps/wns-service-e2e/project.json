{"name": "wns-service-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["wns-service"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "apps/wns-service-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["wns-service:build", "wns-service:serve"]}}}