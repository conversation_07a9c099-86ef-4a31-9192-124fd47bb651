services:
  mongodb:
    image: mongo:7.0
    ports:
      - '27017:27017'
    environment:
      - MONGO_INITDB_DATABASE=herond-notifications
    command: ['--replSet', 'rs0', '--bind_ip_all']
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.sh:/docker-entrypoint-initdb.d/mongo-init.sh:ro
    networks:
      - notification-network

  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    ports:
      - '5672:5672'
      - '15672:15672'
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_USER}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASS}
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - notification-network

  redis:
    image: redis:7.2-alpine
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
    networks:
      - notification-network

volumes:
  mongodb_data:
  rabbitmq_data:
  redis_data:

networks:
  notification-network:
    driver: bridge
