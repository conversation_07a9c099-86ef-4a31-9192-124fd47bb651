# API Documentation

## Base URL

```
http://localhost:3000/api/v1
```

## Database

This API uses **Prisma** as the ORM with **MongoDB** as the database. All data is stored in MongoDB collections with proper indexing for optimal performance.

## Authentication

## Endpoints

### Notifications

#### Send Notification

**POST** `/notifications/send`

Send a notification to one or more recipients across multiple channels.

**Request Body:**

```json
{
  "appCode": "herond_wallet",
  "authorId": "user_admin_001",
  "channels": ["Fcm"],
  "fallbackChannels": ["Sms"],
  "destinations": [
    {
      "userId": "user_12345",
      "deviceTokens": [
        "dgoDITrkQVWe6qT7lgOG9P:APA91bHCHyHGF3GyDU9DqTBs4n6BwZ4QLegL0wW7LETethugvHw9U7XwayMhREqXgqaP7gwPrY8BMs5xlu63riZWbjeWRPekMOFhZ7WYMQtz2USltrFTv_o"
      ]
    },
    {
      "userId": "user_67890",
      "deviceTokens": ["fcm_token_3"]
    }
  ],
  "eventCode": "USER_TRANSFER_ETH",
  "categoryCode": "Transactional",
  "templateCode": "USER_OTP",
  "locale": "en-US",
  "message": {
    "fcm": {
      "subject": "Transaction Alert",
      "content": "Hello {{username}}, you just transferred {{amount}} ETH",
      "android": {
        "priority": "high",
        "notification": {
          "channelId": "transactions",
          "sound": "ping.mp3"
        }
      },
      "apns": {
        "payload": {
          "aps": {
            "badge": 1,
            "sound": "default",
            "interruptionLevel": "time-sensitive"
          }
        }
      }
    },
    "email": {
      "provider": "SesSmtp",
      "subject": "Transaction Alert",
      "content": "<p>Hello Alice, you just transferred 100 ETH</p>",
      "attachments": [
        {
          "filename": "receipt.pdf",
          "url": "https://cdn.example.com/files/receipt.pdf"
        }
      ]
    },
    "data": {
      "username": "Alice",
      "amount": "100",
      "socketId": "abc123"
    }
  },
  "priority": "High",
  "scheduleTime": "2025-08-20T12:00:00Z"
}
```

**Response:**

```json
{
  "type": "Ok",
  "status": 201,
  "code": null,
  "message": null
}
```

#### Get Notification

**GET** `/notifications/{id}`

Retrieve a specific notification by ID.

**Response:**

```json
{}
```

#### List Notifications

**GET** `/notifications`

Retrieve a paginated list of notifications.

**Query Parameters:**

**Response:**

```json
{}
```

### Templates

#### Create Template

**POST** `/templates`

Create a new notification template.

**Request Body:**

```json
{}
```

#### Get Template

**GET** `/templates/{id}`

Retrieve a specific template by ID.

#### List Templates

**GET** `/templates`

Retrieve a paginated list of templates.

#### Update Template

**PUT** `/templates/{id}`

Update an existing template.

#### Delete Template

**DELETE** `/templates/{id}`

Delete a template.

### Health

#### Health Check

**GET** `/health`

Check the health status of the application and all providers.

**Response:**

```json
{}
```

## Error Responses

All endpoints return errors in the following format:

```json
{}
```

## Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `429` - Too Many Requests
- `500` - Internal Server Error

## Rate Limiting

The API implements comprehensive rate limiting to prevent abuse and ensure fair usage:

### Rate Limits by Endpoint

| Endpoint                              | Authenticated Users | Anonymous Users | Time Window |
| ------------------------------------- | ------------------- | --------------- | ----------- |
| `POST /notifications/send`            | 20 requests         | 10 requests     | 1 minute    |
| `POST /notifications/register-device` | 10 requests         | 5 requests      | 5 minutes   |
| `POST /user-preferences`              | 40 requests         | 20 requests     | 1 minute    |
| `GET /user-preferences/:id`           | 40 requests         | 20 requests     | 1 minute    |
| Other endpoints                       | 200 requests        | 100 requests    | 1 minute    |

### Rate Limit Headers

All API responses include rate limiting information in the headers:

```http
X-RateLimit-Limit: 10
X-RateLimit-Remaining: 7
X-RateLimit-Reset: 1640995200
X-RateLimit-Reset-After: 45
Retry-After: 45
```

### Rate Limit Exceeded Response

When rate limits are exceeded, the API returns a `429 Too Many Requests` status code:

```json
{
  "status": "error",
  "statusCode": 429,
  "data": null,
  "message": "Rate limit exceeded. 10 requests allowed per 60 seconds. Consider authenticating for higher limits. Try again after 2024-01-01T12:30:00Z.",
  "meta": {
    "rateLimitInfo": {
      "limit": 10,
      "remaining": 0,
      "resetTime": "2024-01-01T12:30:00Z",
      "resetAfter": 45
    },
    "suggestion": "Consider authenticating for higher limits. Please wait before making more requests."
  }
}
```

### Best Practices

1. **Check Rate Limit Headers**: Always monitor the rate limit headers in responses
2. **Implement Exponential Backoff**: When rate limited, wait before retrying with increasing delays
3. **Authenticate When Possible**: Authenticated users receive higher rate limits
4. **Batch Operations**: Combine multiple operations when possible to reduce API calls
5. **Cache Responses**: Store frequently accessed data to minimize repeated requests

For detailed information, see the [Rate Limiting Documentation](RATE_LIMITING.md).

## Webhooks

The system can send webhooks for notification events:

### Webhook Events

- `notification.sent` - Notification was sent successfully
- `notification.delivered` - Notification was delivered
- `notification.failed` - Notification failed to send
- `notification.clicked` - Notification was clicked (if supported by provider)

### Webhook Payload

```json
{
  "event": "notification.sent",
  "timestamp": "ISO 8601 date string",
  "data": {
    "notificationId": "notification-id",
    "recipientId": "recipient-id",
    "channel": "fcm",
    "status": "sent",
    "metadata": {}
  }
}
```
