# API Documentation

## Base URL

```
http://localhost:3000/api/v1
```

## Database

This API uses **Prisma** as the ORM with **MongoDB** as the database. All data is stored in MongoDB collections with proper indexing for optimal performance.

## Authentication

Currently, the API does not require authentication. In production, you should implement proper authentication mechanisms.

## Endpoints

### Notifications

#### Send Notification

**POST** `/notifications`

Send a notification to one or more recipients across multiple channels.

**Request Body:**

```json
{
  "templateId": "string (optional)",
  "payload": {
    "title": "string (required)",
    "body": "string (required)",
    "data": "object (optional)",
    "imageUrl": "string (optional)",
    "actionUrl": "string (optional)"
  },
  "recipients": [
    {
      "id": "string (required)",
      "channel": "fcm|apns|email|sms|wns (required)",
      "address": "string (required)",
      "metadata": "object (optional)"
    }
  ],
  "channels": ["fcm", "apns", "email", "sms", "wns"],
  "priority": "low|normal|high|critical (required)",
  "scheduledAt": "ISO 8601 date string (optional)",
  "expiresAt": "ISO 8601 date string (optional)",
  "metadata": "object (optional)",
  "createdBy": "string (optional)"
}
```

**Response:**

```json
{
  "id": "notification-id",
  "status": "success",
  "results": [
    {
      "id": "result-id",
      "recipientId": "recipient-id",
      "channel": "fcm",
      "status": "sent|failed|pending",
      "sentAt": "ISO 8601 date string",
      "metadata": {}
    }
  ]
}
```

#### Get Notification

**GET** `/notifications/{id}`

Retrieve a specific notification by ID.

**Response:**

```json
{
  "id": "notification-id",
  "payload": {},
  "recipients": [],
  "channels": [],
  "priority": "normal",
  "status": "sent",
  "createdAt": "ISO 8601 date string",
  "updatedAt": "ISO 8601 date string"
}
```

#### List Notifications

**GET** `/notifications`

Retrieve a paginated list of notifications.

**Query Parameters:**

- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `channel`: Filter by channel
- `recipientId`: Filter by recipient ID
- `dateFrom`: Filter by date from
- `dateTo`: Filter by date to

**Response:**

```json
{
  "data": [],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "pages": 10
  }
}
```

### Templates

#### Create Template

**POST** `/templates`

Create a new notification template.

**Request Body:**

```json
{
  "name": "string (required)",
  "description": "string (optional)",
  "channels": ["fcm", "email"],
  "payload": {
    "title": "string (required)",
    "body": "string (required)",
    "data": "object (optional)",
    "imageUrl": "string (optional)",
    "actionUrl": "string (optional)"
  },
  "variables": ["username", "email"]
}
```

#### Get Template

**GET** `/templates/{id}`

Retrieve a specific template by ID.

#### List Templates

**GET** `/templates`

Retrieve a paginated list of templates.

#### Update Template

**PUT** `/templates/{id}`

Update an existing template.

#### Delete Template

**DELETE** `/templates/{id}`

Delete a template.

### Health

#### Health Check

**GET** `/health`

Check the health status of the application and all providers.

**Response:**

```json
{
  "status": "ok",
  "timestamp": "ISO 8601 date string",
  "uptime": 12345,
  "providers": {
    "fcm": true,
    "apns": true,
    "email": false,
    "sms": true,
    "wns": true
  },
  "database": {
    "status": "connected",
    "responseTime": 5
  },
  "queue": {
    "status": "connected",
    "responseTime": 3
  }
}
```

## Error Responses

All endpoints return errors in the following format:

```json
{
  "statusCode": 400,
  "message": "Error description",
  "error": "Bad Request",
  "timestamp": "ISO 8601 date string",
  "path": "/api/v1/notifications"
}
```

## Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

## Rate Limiting

The API implements rate limiting to prevent abuse:

- 100 requests per minute per IP address
- 1000 requests per hour per IP address

When rate limits are exceeded, the API returns a `429 Too Many Requests` status code.

## Webhooks

The system can send webhooks for notification events:

### Webhook Events

- `notification.sent` - Notification was sent successfully
- `notification.delivered` - Notification was delivered
- `notification.failed` - Notification failed to send
- `notification.clicked` - Notification was clicked (if supported by provider)

### Webhook Payload

```json
{
  "event": "notification.sent",
  "timestamp": "ISO 8601 date string",
  "data": {
    "notificationId": "notification-id",
    "recipientId": "recipient-id",
    "channel": "fcm",
    "status": "sent",
    "metadata": {}
  }
}
```
