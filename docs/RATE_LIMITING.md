# Rate Limiting

The Herond Notification Center implements comprehensive rate limiting to prevent abuse and ensure fair usage of the API. This document describes the rate limiting implementation, configuration, and best practices.

## Overview

Rate limiting is implemented using:
- **NestJS Throttler**: Core rate limiting functionality
- **Redis Storage**: Distributed rate limiting across multiple instances
- **Custom Guards**: Enhanced rate limiting with user-based tracking
- **Endpoint-specific Limits**: Different limits for different operations

## Rate Limits

### Default Limits

| Endpoint | Authenticated Users | Anonymous Users | Time Window |
|----------|-------------------|-----------------|-------------|
| Send Notification | 20 requests | 10 requests | 1 minute |
| Register Device | 10 requests | 5 requests | 5 minutes |
| User Preferences | 40 requests | 20 requests | 1 minute |
| General API | 200 requests | 100 requests | 1 minute |

### Rate Limit Headers

All API responses include rate limiting headers:

```http
X-RateLimit-Limit: 10
X-RateLimit-Remaining: 7
X-RateLimit-Reset: 1640995200
X-RateLimit-Reset-After: 45
Retry-After: 45
```

- `X-RateLimit-Limit`: Maximum requests allowed in the time window
- `X-RateLimit-Remaining`: Remaining requests in the current window
- `X-RateLimit-Reset`: Unix timestamp when the rate limit resets
- `X-RateLimit-Reset-After`: Seconds until the rate limit resets
- `Retry-After`: Seconds to wait before making another request

## Configuration

### Environment Variables

```bash
# Global rate limiting (fallback for all endpoints)
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# Endpoint-specific rate limiting
RATE_LIMIT_SEND_TTL=60
RATE_LIMIT_SEND_LIMIT=10
RATE_LIMIT_REGISTER_TTL=300
RATE_LIMIT_REGISTER_LIMIT=5
RATE_LIMIT_PREFERENCES_TTL=60
RATE_LIMIT_PREFERENCES_LIMIT=20

# Authenticated user multiplier (higher limits for authenticated users)
RATE_LIMIT_AUTH_MULTIPLIER=2

# Redis configuration for distributed rate limiting
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_KEY_PREFIX=rate-limit:

# Rate limiting behavior
RATE_LIMIT_SKIP_SUCCESS=false
RATE_LIMIT_SKIP_FAILED=false
RATE_LIMIT_IGNORE_USER_AGENTS=
```

### Programmatic Configuration

```typescript
import { RateLimitingModule } from './infrastructure/rate-limiting';

@Module({
  imports: [
    RateLimitingModule,
    // ... other modules
  ],
})
export class AppModule {}
```

## Usage

### Controller Decorators

```typescript
import { 
  NotificationSendRateLimit, 
  DeviceRegistrationRateLimit,
  UserPreferencesRateLimit 
} from './infrastructure/rate-limiting';

@Controller('notifications')
export class NotificationsController {
  @Post('send')
  @NotificationSendRateLimit()
  async sendNotification() {
    // Implementation
  }

  @Post('register-device')
  @DeviceRegistrationRateLimit()
  async registerDevice() {
    // Implementation
  }
}
```

### Custom Rate Limits

```typescript
import { Throttle } from '@nestjs/throttler';

@Controller('custom')
export class CustomController {
  @Get('endpoint')
  @Throttle({ default: { ttl: 30000, limit: 5 } }) // 5 requests per 30 seconds
  async customEndpoint() {
    // Implementation
  }
}
```

## Error Handling

### Rate Limit Exceeded Response

When rate limits are exceeded, the API returns a `429 Too Many Requests` status:

```json
{
  "status": "error",
  "statusCode": 429,
  "data": null,
  "message": "Rate limit exceeded. 10 requests allowed per 60 seconds. Consider authenticating for higher limits. Try again after 2024-01-01T12:30:00Z.",
  "meta": {
    "rateLimitInfo": {
      "limit": 10,
      "remaining": 0,
      "resetTime": "2024-01-01T12:30:00Z",
      "resetAfter": 45
    },
    "suggestion": "Consider authenticating for higher limits. Please wait before making more requests."
  }
}
```

## Best Practices

### For API Clients

1. **Check Rate Limit Headers**: Always check the rate limit headers in responses
2. **Implement Exponential Backoff**: When rate limited, wait before retrying
3. **Authenticate When Possible**: Authenticated users get higher limits
4. **Batch Requests**: Combine multiple operations when possible
5. **Cache Responses**: Avoid unnecessary repeated requests

### Example Client Implementation

```javascript
class NotificationClient {
  async sendNotification(data) {
    try {
      const response = await fetch('/api/v1/notifications/send', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      });

      // Check rate limit headers
      const remaining = response.headers.get('X-RateLimit-Remaining');
      const resetAfter = response.headers.get('X-RateLimit-Reset-After');

      if (response.status === 429) {
        // Rate limited - wait and retry
        const retryAfter = parseInt(response.headers.get('Retry-After') || '60');
        await this.sleep(retryAfter * 1000);
        return this.sendNotification(data); // Retry
      }

      return response.json();
    } catch (error) {
      console.error('Failed to send notification:', error);
      throw error;
    }
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

## Monitoring

### Metrics

The rate limiting system provides metrics for monitoring:

- Rate limit violations per endpoint
- Top rate-limited users/IPs
- Rate limiting effectiveness
- Redis storage performance

### Logging

Rate limit violations are logged with the following information:

```json
{
  "level": "warn",
  "message": "Rate limit exceeded for *********** on /api/v1/notifications/send",
  "ip": "***********",
  "userAgent": "Mozilla/5.0...",
  "url": "/api/v1/notifications/send",
  "method": "POST",
  "userId": "user123",
  "limit": 10,
  "remaining": 0,
  "resetTime": 1640995200
}
```

## Troubleshooting

### Common Issues

1. **Redis Connection Issues**: Ensure Redis is running and accessible
2. **High Memory Usage**: Monitor Redis memory usage and configure appropriate TTLs
3. **False Positives**: Check if legitimate traffic is being rate limited
4. **Inconsistent Limits**: Verify configuration across all instances

### Debugging

Enable debug logging to troubleshoot rate limiting issues:

```bash
LOG_LEVEL=debug
```

This will log detailed information about rate limiting decisions and Redis operations.

## Security Considerations

1. **IP Spoofing**: Use trusted proxy headers carefully
2. **Distributed Attacks**: Monitor for distributed rate limit violations
3. **Resource Exhaustion**: Ensure Redis has appropriate memory limits
4. **Bypass Attempts**: Monitor for attempts to bypass rate limiting

## Performance

### Redis Optimization

- Use Redis clustering for high availability
- Configure appropriate memory policies
- Monitor Redis performance metrics
- Use connection pooling

### Application Optimization

- Minimize rate limiting overhead
- Use efficient key structures
- Implement proper error handling
- Monitor application performance impact
