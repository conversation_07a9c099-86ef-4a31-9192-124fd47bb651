import { Catch, HttpException, type ArgumentsHost, type ExceptionFilter } from '@nestjs/common';
import { ApiResponse } from '../../types/api-response';
import { type Response } from 'express';

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const status = exception.getStatus();

    const exceptionResponse: any = exception.getResponse();
    const message =
      typeof exceptionResponse === 'string' ? exceptionResponse : exceptionResponse.message;

    const apiResponse = new ApiResponse('error', status, null, message, null);

    response.status(status).json(apiResponse);
  }
}
