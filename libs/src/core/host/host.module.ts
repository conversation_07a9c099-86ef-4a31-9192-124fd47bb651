import { Module } from '@nestjs/common';
import { APP_FILTER, APP_INTERCEPTOR } from '@nestjs/core';
import { ResultInterceptor } from './interceptors/result.interceptor';
import { HttpExceptionFilter } from './exception-filters/http-exception.filter';

const globalProviders = [
  {
    provide: APP_INTERCEPTOR,
    useClass: ResultInterceptor,
  },
  {
    provide: APP_FILTER,
    useClass: HttpExceptionFilter,
  },
];

@Module({
  providers: [...globalProviders],
})
export class HostModule {}
