import { Injectable } from '@nestjs/common';
import type { ExecutionContext } from '@nestjs/common';
import {
  ThrottlerGuard,
  type Resolvable,
  type ThrottlerGenerateKeyFunction,
  type ThrottlerGetTrackerFunction,
  type ThrottlerLimitDetail,
} from '@nestjs/throttler';
import { RATE_LIMIT_KEY } from './rate-limiting.decorators';

// This is copied from @nestjs/throttler to customize activation logic
export const THROTTLER_LIMIT = 'THROTTLER:LIMIT';
export const THROTTLER_TTL = 'THROTTLER:TTL';
export const THROTTLER_TRACKER = 'THROTTLER:TRACKER';
export const THROTTLER_BLOCK_DURATION = 'THROTTLER:BLOCK_DURATION';
export const THROTTLER_KEY_GENERATOR = 'THROTTLER:KEY_GENERATOR';
export const THROTTLER_OPTIONS = 'THROTTLER:MODULE_OPTIONS';
export const THROTTLER_SKIP = 'THROTTLER:SKIP';

@Injectable()
export class RateLimitGuard extends ThrottlerGuard {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const handler = context.getHandler();
    const classRef = context.getClass();

    if (await this.shouldSkip(context)) {
      return true;
    }
    const continues: boolean[] = [];

    for (const namedThrottler of this.throttlers) {
      // Return early if the current route should be skipped.
      const skip = this.reflector.getAllAndOverride<boolean>(THROTTLER_SKIP + namedThrottler.name, [
        handler,
        classRef,
      ]);

      const activeKeys = this.reflector.getAllAndOverride<string[]>(RATE_LIMIT_KEY, [
        handler,
        classRef,
      ]) ?? ['default'];

      const skipIf = namedThrottler.skipIf || this.commonOptions.skipIf;
      if (skip || skipIf?.(context) || !activeKeys.includes(namedThrottler.name)) {
        continues.push(true);
        continue;
      }

      // Return early when we have no limit or ttl data.
      const routeOrClassLimit = this.reflector.getAllAndOverride<Resolvable<number>>(
        THROTTLER_LIMIT + namedThrottler.name,
        [handler, classRef]
      );
      const routeOrClassTtl = this.reflector.getAllAndOverride<Resolvable<number>>(
        THROTTLER_TTL + namedThrottler.name,
        [handler, classRef]
      );
      const routeOrClassBlockDuration = this.reflector.getAllAndOverride<Resolvable<number>>(
        THROTTLER_BLOCK_DURATION + namedThrottler.name,
        [handler, classRef]
      );
      const routeOrClassGetTracker = this.reflector.getAllAndOverride<ThrottlerGetTrackerFunction>(
        THROTTLER_TRACKER + namedThrottler.name,
        [handler, classRef]
      );
      const routeOrClassGetKeyGenerator =
        this.reflector.getAllAndOverride<ThrottlerGenerateKeyFunction>(
          THROTTLER_KEY_GENERATOR + namedThrottler.name,
          [handler, classRef]
        );

      // Check if specific limits are set at class or route level, otherwise use global options.
      const limit = await this.resolve(context, routeOrClassLimit || namedThrottler.limit);
      const ttl = await this.resolve(context, routeOrClassTtl || namedThrottler.ttl);
      const blockDuration = await this.resolve(
        context,
        routeOrClassBlockDuration || namedThrottler.blockDuration || ttl
      );
      const getTracker =
        routeOrClassGetTracker || namedThrottler.getTracker || this.commonOptions.getTracker;
      const generateKey =
        routeOrClassGetKeyGenerator || namedThrottler.generateKey || this.commonOptions.generateKey;
      continues.push(
        await this.handleRequest({
          context,
          limit,
          ttl,
          throttler: namedThrottler,
          blockDuration,
          getTracker,
          generateKey,
        })
      );
    }
    return continues.every(cont => cont);
  }

  protected async getTracker(req: Record<string, unknown>): Promise<string> {
    const authenticatedReq = req as any;
    // Use user ID for authenticated requests, IP for anonymous
    if (authenticatedReq.user?.userId) {
      return `user:${authenticatedReq.user.userId}`;
    }

    // Fallback to IP-based tracking
    return (req.ip as string) || (req.connection as any)?.remoteAddress || 'unknown';
  }

  protected async shouldSkip(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    // Skip for health check endpoints
    const url = (request.url as string) || '';
    if (url.includes('/health') || url.includes('/version')) {
      return true;
    }

    return false;
  }

  private async resolve<T extends number | string | boolean>(
    context: ExecutionContext,
    resolvableValue: Resolvable<T>
  ): Promise<T> {
    return typeof resolvableValue === 'function' ? resolvableValue(context) : resolvableValue;
  }

  protected getErrorMessage(
    context: ExecutionContext,
    throttlerLimitDetail: ThrottlerLimitDetail
  ): Promise<string> {
    return Promise.resolve(
      `${throttlerLimitDetail.limit} requests allowed per ${
        throttlerLimitDetail.ttl / 1000
      } seconds.`
    );
  }
}
