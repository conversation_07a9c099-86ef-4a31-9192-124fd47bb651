import { registerAs } from '@nestjs/config';

export interface RateLimitingConfig {
  global: {
    ttl: number; // Time window in seconds
    limit: number; // Max requests per window
  };

  // Redis configuration for distributed rate limiting
  redis: {
    host: string;
    port: number;
    password?: string;
    db: number;
    keyPrefix: string;
  };
}

export default registerAs(
  'rateLimiting',
  (): RateLimitingConfig => ({
    global: {
      ttl: parseInt(process.env.RATE_LIMIT_TTL || '60', 10), // 60 seconds
      limit: parseInt(process.env.RATE_LIMIT_LIMIT || '100', 10), // 100 requests
    },

    redis: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379', 10),
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_DB || '0', 10),
      keyPrefix: process.env.REDIS_KEY_PREFIX || 'rate-limit:',
    },
  })
);
