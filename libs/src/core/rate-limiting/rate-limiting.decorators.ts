import { applyDecorators, SetMetadata } from '@nestjs/common';
import { Throttle } from '@nestjs/throttler';

export const RATE_LIMIT_KEY = 'rate-limit';

export interface RateLimitOptions {
  ttl: number;
  limit: number;
}

type Keys = readonly string[];
type RateLimitFunc = <const K extends Keys>(
  keys: K,
  options?: Record<K[number], RateLimitOptions>
) => MethodDecorator & ClassDecorator;

export const RateLimit: RateLimitFunc = (keys, options) =>
  applyDecorators(SetMetadata(RATE_LIMIT_KEY, keys), Throttle({ ...options }));
