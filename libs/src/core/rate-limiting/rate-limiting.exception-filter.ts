import { Catch, HttpStatus, Logger } from '@nestjs/common';
import type { ExceptionFilter, ArgumentsHost } from '@nestjs/common';
import { ThrottlerException } from '@nestjs/throttler';
import type { Response } from 'express';
import { ApiResponse } from '../types/api-response';

@Catch(ThrottlerException)
export class RateLimitingExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(RateLimitingExceptionFilter.name);

  catch(exception: ThrottlerException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest();

    const status = HttpStatus.TOO_MANY_REQUESTS;
    const message = exception.message || 'Rate limit exceeded';

    // Extract rate limit information from the exception context
    const rateLimitInfo = this.extractRateLimitInfo(exception);

    // Set rate limiting headers
    response.set({
      'X-RateLimit-Limit': rateLimitInfo.limit.toString(),
      'X-RateLimit-Remaining': Math.max(0, rateLimitInfo.remaining).toString(),
      'X-RateLimit-Reset': rateLimitInfo.resetTime.toString(),
      'X-RateLimit-Reset-After': Math.ceil(rateLimitInfo.resetAfter / 1000).toString(),
      'Retry-After': Math.ceil(rateLimitInfo.resetAfter / 1000).toString(),
    });

    // Log the rate limit violation
    this.logger.warn(`Rate limit exceeded for ${request.ip || 'unknown'} on ${request.url}`, {
      ip: request.ip,
      userAgent: request.headers['user-agent'],
      url: request.url,
      method: request.method,
      userId: request.user?.sub || 'anonymous',
      limit: rateLimitInfo.limit,
      remaining: rateLimitInfo.remaining,
      resetTime: rateLimitInfo.resetTime,
    });

    const apiResponse = new ApiResponse('error', status, null, message, {
      limit: rateLimitInfo.limit,
      remaining: rateLimitInfo.remaining,
      resetTime: new Date(rateLimitInfo.resetTime).toISOString(),
      resetAfter: Math.ceil(rateLimitInfo.resetAfter / 1000),
      suggestion: rateLimitInfo.suggestion,
    });

    response.status(status).json(apiResponse);
  }

  private extractRateLimitInfo(exception: ThrottlerException) {
    // Default values
    let limit = 100;
    const remaining = 0;
    let resetTime = Date.now() + 60000; // 1 minute from now
    let resetAfter = 60000; // 1 minute

    // Try to extract information from the exception message or context
    const message = exception.message || '';

    // Parse limit from message if available
    const limitMatch = message.match(/(\d+) requests allowed/);
    if (limitMatch) {
      limit = parseInt(limitMatch[1], 10);
    }

    // Parse time window from message if available
    const timeMatch = message.match(/per (\d+) seconds/);
    if (timeMatch) {
      const timeWindow = parseInt(timeMatch[1], 10) * 1000;
      resetTime = Date.now() + timeWindow;
      resetAfter = timeWindow;
    }

    return {
      limit,
      remaining,
      resetTime,
      resetAfter,
      suggestion: 'You have reached your rate limit. Please wait before making more requests.',
    };
  }
}
