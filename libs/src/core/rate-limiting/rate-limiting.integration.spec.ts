import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';
import { RateLimitingModule } from './rate-limiting.module';
import rateLimitingConfig from './rate-limiting.config';
import * as request from 'supertest';
import { Controller, Get, UseGuards } from '@nestjs/common';
import { RateLimitGuard } from './custom-throttler.guard';
import { NotificationSendRateLimit } from './rate-limiting.decorators';

// Test controller for rate limiting
@Controller('test')
class TestController {
  @Get('unlimited')
  unlimited() {
    return { message: 'success' };
  }

  @Get('limited')
  @UseGuards(RateLimitGuard)
  @NotificationSendRateLimit()
  limited() {
    return { message: 'success' };
  }
}

describe('Rate Limiting Integration', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          load: [rateLimitingConfig],
          isGlobal: true,
        }),
        RateLimitingModule,
      ],
      controllers: [TestController],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Rate limiting behavior', () => {
    it('should allow requests under the limit', async () => {
      const response = await request(app.getHttpServer()).get('/test/unlimited').expect(200);

      expect(response.body).toEqual({ message: 'success' });
    });

    it('should apply rate limiting to protected endpoints', async () => {
      // Make multiple requests quickly
      const promises = Array.from({ length: 15 }, () =>
        request(app.getHttpServer()).get('/test/limited')
      );

      const responses = await Promise.all(promises);

      // Some requests should succeed (under limit)
      const successfulRequests = responses.filter(r => r.status === 200);
      const rateLimitedRequests = responses.filter(r => r.status === 429);

      expect(successfulRequests.length).toBeLessThanOrEqual(10); // Limit is 10
      expect(rateLimitedRequests.length).toBeGreaterThan(0);

      // Check rate limit headers on rate limited responses
      if (rateLimitedRequests.length > 0) {
        const rateLimitedResponse = rateLimitedRequests[0];
        expect(rateLimitedResponse.headers['x-ratelimit-limit']).toBeDefined();
        expect(rateLimitedResponse.headers['x-ratelimit-remaining']).toBeDefined();
        expect(rateLimitedResponse.headers['x-ratelimit-reset']).toBeDefined();
        expect(rateLimitedResponse.headers['retry-after']).toBeDefined();
      }
    });

    it('should include proper error message for rate limited requests', async () => {
      // First, exhaust the rate limit
      const promises = Array.from({ length: 15 }, () =>
        request(app.getHttpServer()).get('/test/limited')
      );

      const responses = await Promise.all(promises);
      const rateLimitedResponse = responses.find(r => r.status === 429);

      if (rateLimitedResponse) {
        expect(rateLimitedResponse.body).toHaveProperty('status', 'error');
        expect(rateLimitedResponse.body).toHaveProperty('statusCode', 429);
        expect(rateLimitedResponse.body.message).toContain('Rate limit exceeded');
        expect(rateLimitedResponse.body).toHaveProperty('meta');
        expect(rateLimitedResponse.body.meta).toHaveProperty('rateLimitInfo');
      }
    });
  });

  describe('Health check endpoints', () => {
    it('should skip rate limiting for health endpoints', async () => {
      // This would normally be handled by the health controller
      // but we're testing the skip logic
      const promises = Array.from({ length: 20 }, () =>
        request(app.getHttpServer()).get('/health')
      );

      const responses = await Promise.all(promises);

      // All requests should return 404 (not found) rather than 429 (rate limited)
      // because health endpoints are skipped from rate limiting
      responses.forEach(response => {
        expect(response.status).not.toBe(429);
      });
    });
  });
});
