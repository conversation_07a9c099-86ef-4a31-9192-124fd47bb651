import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { Reflector } from '@nestjs/core';
import { RateLimitGuard } from './custom-throttler.guard';
import { ExecutionContext } from '@nestjs/common';
import { AuthenticatedRequest } from '../auth/types';

describe('CustomThrottlerGuard', () => {
  let guard: RateLimitGuard;
  let reflector: Reflector;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RateLimitGuard,
        {
          provide: Reflector,
          useValue: {
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    guard = module.get<RateLimitGuard>(RateLimitGuard);
    reflector = module.get<Reflector>(Reflector);
  });

  it('should be defined', () => {
    expect(guard).toBeDefined();
  });

  describe('getTracker', () => {
    it('should return user ID for authenticated requests', async () => {
      const req = {
        user: { userId: 'user123', type: 'user', scopes: [], issuer: 'test' },
        ip: '127.0.0.1',
      } as AuthenticatedRequest;

      const tracker = await guard['getTracker'](req);
      expect(tracker).toBe('user:user123');
    });

    it('should return IP for anonymous requests', async () => {
      const req = {
        ip: '127.0.0.1',
        connection: { remoteAddress: '***********' },
      };

      const tracker = await guard['getTracker'](req);
      expect(tracker).toBe('127.0.0.1');
    });

    it('should return connection remote address if no IP', async () => {
      const req = {
        connection: { remoteAddress: '***********' },
      };

      const tracker = await guard['getTracker'](req);
      expect(tracker).toBe('***********');
    });

    it('should return unknown if no IP or connection', async () => {
      const req = {};

      const tracker = await guard['getTracker'](req);
      expect(tracker).toBe('unknown');
    });
  });

  describe('shouldSkip', () => {
    it('should skip health check endpoints', async () => {
      const mockContext = {
        switchToHttp: () => ({
          getRequest: () => ({ url: '/api/v1/health' }),
        }),
      } as ExecutionContext;

      const shouldSkip = await guard['shouldSkip'](mockContext);
      expect(shouldSkip).toBe(true);
    });

    it('should skip version endpoints', async () => {
      const mockContext = {
        switchToHttp: () => ({
          getRequest: () => ({ url: '/version' }),
        }),
      } as ExecutionContext;

      const shouldSkip = await guard['shouldSkip'](mockContext);
      expect(shouldSkip).toBe(true);
    });

    it('should not skip regular endpoints', async () => {
      const mockContext = {
        switchToHttp: () => ({
          getRequest: () => ({ url: '/api/v1/notifications/send' }),
        }),
      } as ExecutionContext;

      const shouldSkip = await guard['shouldSkip'](mockContext);
      expect(shouldSkip).toBe(false);
    });
  });
});
