import { ThrottlerStorage } from '@nestjs/throttler';
import Keyv from 'keyv';
import KeyvRedis from '@keyv/redis';
import { Logger } from '@nestjs/common';

interface ThrottlerStorageRecord {
  totalHits: number;
  timeToExpire: number;
  isBlocked: boolean;
  timeToBlockExpire: number;
}

export interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db: number;
  keyPrefix: string;
}

export class RateLimitingStorage implements ThrottlerStorage {
  private readonly logger = new Logger(RateLimitingStorage.name);
  private readonly keyv: Keyv;

  constructor(private readonly config: RedisConfig) {
    const redisStore = new KeyvRedis({
      host: config.host,
      port: config.port,
      password: config.password,
      db: config.db,
    });

    this.keyv = new Keyv({
      store: redisStore,
      namespace: config.keyPrefix,
    });

    this.keyv.on('error', error => {
      this.logger.error('Redis connection error in rate limiting storage', error);
    });

    this.logger.log(
      `Rate limiting storage initialized with Redis at ${config.host}:${config.port}`
    );
  }

  async increment(
    key: string,
    ttl: number,
    limit: number,
    blockDuration: number,
    throttlerName: string
  ): Promise<ThrottlerStorageRecord> {
    try {
      const current = await this.keyv.get(key);
      const now = Date.now();

      if (!current) {
        // First request - set initial value
        const record = {
          count: 1,
          resetTime: now + ttl,
        };

        await this.keyv.set(key, record, ttl);

        return {
          totalHits: 1,
          timeToExpire: ttl,
          isBlocked: false,
          timeToBlockExpire: 0,
        };
      }

      // Check if the window has expired
      if (now >= current.resetTime) {
        // Window expired, reset counter
        const record = {
          count: 1,
          resetTime: now + ttl,
        };

        await this.keyv.set(key, record, ttl);

        return {
          totalHits: 1,
          timeToExpire: ttl,
          isBlocked: false,
          timeToBlockExpire: 0,
        };
      }

      // Increment counter within the same window
      const record = {
        count: current.count + 1,
        resetTime: current.resetTime,
      };

      const remainingTtl = Math.max(0, current.resetTime - now);
      await this.keyv.set(key, record, remainingTtl);

      const isBlocked = record.count >= limit;

      return {
        totalHits: record.count,
        timeToExpire: remainingTtl,
        isBlocked,
        timeToBlockExpire: isBlocked ? remainingTtl : 0,
      };
    } catch (error) {
      this.logger.error(`Error incrementing rate limit counter for key ${key}`, error);

      // Fallback: allow the request but log the error
      return {
        totalHits: 0,
        timeToExpire: ttl,
        isBlocked: false,
        timeToBlockExpire: 0,
      };
    }
  }

  async reset(key: string): Promise<void> {
    try {
      await this.keyv.delete(key);
      this.logger.debug(`Rate limit counter reset for key: ${key}`);
    } catch (error) {
      this.logger.error(`Error resetting rate limit counter for key ${key}`, error);
    }
  }

  async getRecord(key: string): Promise<{ totalHits: number; timeToExpire: number } | undefined> {
    try {
      const record = await this.keyv.get(key);

      if (!record) {
        return undefined;
      }

      const now = Date.now();

      if (now >= record.resetTime) {
        // Record expired
        await this.keyv.delete(key);
        return undefined;
      }

      return {
        totalHits: record.count,
        timeToExpire: Math.max(0, record.resetTime - now),
      };
    } catch (error) {
      this.logger.error(`Error getting rate limit record for key ${key}`, error);
      return undefined;
    }
  }

  async shutdown(): Promise<void> {
    try {
      await this.keyv.disconnect();
      this.logger.log('Rate limiting storage disconnected');
    } catch (error) {
      this.logger.error('Error shutting down rate limiting storage', error);
    }
  }
}
