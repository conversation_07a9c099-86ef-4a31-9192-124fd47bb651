import { Global, Module } from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { NotificationRepository } from './repositories/notification.repository';
import { DeviceRepository } from './repositories/device.repository';
import { NotificationSnapshotRepository } from './repositories/notification-snapshot.repository';
import { UserPreferencesRepository } from './repositories/user-preferences.repository';

@Global()
@Module({
  providers: [
    PrismaService,
    DeviceRepository,
    NotificationRepository,
    NotificationSnapshotRepository,
    UserPreferencesRepository,
  ],
  exports: [
    PrismaService,
    DeviceRepository,
    NotificationRepository,
    NotificationSnapshotRepository,
    UserPreferencesRepository,
  ],
})
export class DatabaseModule {}
