import type { NotificationStatus } from '../../shared';
import type { NotificationQueueMessage } from '../../shared/interfaces/notification-queue-message.interface';

export interface NotificationMessageSnapshot extends NotificationQueueMessage {
  status: NotificationStatus;
  sentAt?: Date;
  deliveredAt?: Date;
  failureReason?: string;
  metadata?: unknown;
  retryCount: number;
  maxRetries: number;
  createdAt: Date;
  scheduledAt?: Date;
  expiresAt?: Date;
}
