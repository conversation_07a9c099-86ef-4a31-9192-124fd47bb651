import { Injectable } from '@nestjs/common';
import type { Device } from '../models/device';
import { PrismaService } from '../prisma.service';

@Injectable()
export class DeviceRepository {
  constructor(private readonly prisma: PrismaService) {}

  async registerDevice(device: Device) {
    return this.prisma.device.create({
      data: device,
    });
  }

  async getUserTokenDevices(usersId: string[], appCode: string) {
    return this.prisma.device.findMany({
      where: {
        userId: {
          in: usersId,
        },
        appCode: appCode,
      },
      select: {
        id: true,
        deviceToken: true,
        userId: true,
      },
    });
  }

  async findBy(where: Partial<Device>): Promise<Device | null> {
    return this.prisma.device.findFirst({
      where,
    });
  }

  async refreshDeviceToken(id: string, deviceToken: string) {
    return this.prisma.device.update({
      where: { id },
      data: {
        tokenRefreshedAt: new Date(),
        deviceToken: deviceToken,
      },
    });
  }

  async deleteById(id: string, userId: string) {
    return this.prisma.device.deleteMany({
      where: { id, userId },
    });
  }
}
