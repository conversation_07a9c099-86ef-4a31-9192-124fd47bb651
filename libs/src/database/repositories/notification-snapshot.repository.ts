import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import { Prisma, NotificationStatus, NotificationPriority } from 'prisma-client';
import type { NotificationMessageSnapshot } from '../models/notification-message-snapshot';

@Injectable()
export class NotificationSnapshotRepository {
  /**
   *
   */
  constructor(private readonly prisma: PrismaService) {}

  async createMessagesSnapshot(messages: NotificationMessageSnapshot[]) {
    const data: Prisma.NotificationMessageSnapshotCreateManyInput[] = messages.map(x => ({
      id: x.id,
      channel: x.channel,
      priority: x.priority as NotificationPriority,
      fallbackChannels: x.fallbackChannels,
      authorId: x.authorId,
      templateCode: x.templateCode,
      payload: x.payload as any,
      recipient: x.recipient as any,
      status: x.status as NotificationStatus,
      retryCount: x.retryCount,
      maxRetries: x.maxRetries,
      createdAt: x.createdAt,
      scheduledAt: x.scheduledAt,
      expiresAt: x.expiresAt,
    }));
    return this.prisma.notificationMessageSnapshot.createMany({
      data,
    });
  }

  async update(notificationId: string, data: Prisma.NotificationMessageSnapshotUpdateInput) {
    return this.prisma.notificationMessageSnapshot.update({
      where: { id: notificationId },
      data: data,
    });
  }
}
