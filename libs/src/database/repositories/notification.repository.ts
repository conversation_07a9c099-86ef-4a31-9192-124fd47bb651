import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import type { Notification } from '../models';
import { NotificationChannel, Prisma } from 'prisma-client';
@Injectable()
export class NotificationRepository {
  constructor(private readonly prisma: PrismaService) {}
  async create(data: Notification) {
    return this.prisma.notification.create({
      data,
    });
  }
  async findById(id: string) {
    return this.prisma.notification.findUnique({
      where: { id },
    });
  }

  async findMany(
    where?: Prisma.NotificationWhereInput,
    orderBy?: Prisma.NotificationOrderByWithRelationInput,
    skip?: number,
    take?: number
  ) {
    return this.prisma.notification.findMany({
      where,
      orderBy,
      skip,
      take,
    });
  }

  async update(id: string, data: Prisma.NotificationUpdateInput) {
    return this.prisma.notification.update({
      where: { id },
      data,
    });
  }

  async delete(id: string) {
    return this.prisma.notification.delete({
      where: { id },
    });
  }

  async count(where?: Prisma.NotificationWhereInput): Promise<number> {
    return this.prisma.notification.count({
      where,
    });
  }

  async findByRecipientId(recipientId: string, skip?: number, take?: number) {
    return this.prisma.notification.findMany({
      where: {
        recipientId: recipientId,
      },
      skip,
      take,
      orderBy: { deliveredAt: 'desc' },
    });
  }

  async findByChannel(channel: NotificationChannel, skip?: number, take?: number) {
    return this.prisma.notification.findMany({
      where: {
        channel: channel,
      },
      skip,
      take,
      orderBy: { deliveredAt: 'desc' },
    });
  }
}
