import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import type { UserPreferences } from '../models/user-preferences';

@Injectable()
export class UserPreferencesRepository {
  constructor(protected readonly prisma: PrismaService) {}

  public get readPrismaService(): PrismaService {
    return this.prisma;
  }

  async create(data: UserPreferences) {
    return this.prisma.userPreferences.create({
      data: {
        userId: data.userId,
        appCode: data.appCode,
        allowNotifications: data.allowNotifications,
        scopes: data.scopes ?? [],
        options: data.options ?? [],
      },
    });
  }

  async upsert(dto: UserPreferences) {
    return this.prisma.userPreferences.upsert({
      where: { id: dto.id ?? '' },
      update: {
        userId: dto.userId,
        appCode: dto.appCode,
        allowNotifications: dto.allowNotifications,
        scopes: dto.scopes ?? [],
        options: dto.options ?? [],
      },
      create: {
        userId: dto.userId,
        appCode: dto.appCode,
        allowNotifications: dto.allowNotifications,
        scopes: dto.scopes ?? [],
        options: dto.options ?? [],
      },
    });
  }
}
