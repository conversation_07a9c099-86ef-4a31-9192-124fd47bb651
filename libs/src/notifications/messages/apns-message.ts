import {
  NotificationChannel,
  type NotificationPayload,
  type NotificationRecipient,
} from '../../shared';
import { NotificationMessage } from './notification-message';

export class ApnsMessage extends NotificationMessage {
  getChannelSpecificPayload(): NotificationPayload {
    throw new Error('Method not implemented.');
  }

  getRecipient(): NotificationRecipient {
    throw new Error('Method not implemented.');
  }
  channel: NotificationChannel;
  validateRecipient(recipient: NotificationRecipient): boolean {
    throw new Error('Method not implemented.');
  }
}
