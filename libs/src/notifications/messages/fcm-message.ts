import {
  NotificationChannel,
  type FcmPayload,
  type NotificationPayload,
  type NotificationQueueMessage,
  type NotificationRecipient,
} from '../../shared';
import { NotificationMessage } from './notification-message';

export class FcmMessage extends NotificationMessage<FcmPayload> {
  getChannelSpecificPayload(payload: NotificationPayload): FcmPayload {
    return {
      subject: payload.subject || '',
      content: payload.content || '',
      data: payload.data || {},
      android: payload.android,
      apns: payload.apns,
      webpush: payload.webpush,
    };
  }

  readonly channel: NotificationChannel = NotificationChannel.Fcm;

  constructor(message: NotificationQueueMessage) {
    super(message);
  }

  getRecipientTokens(): Array<string> {
    if (Array.isArray(this.recipient.address)) {
      return this.recipient.address;
    }
    return [this.recipient.address as string];
  }

  validateRecipient(recipient: NotificationRecipient): boolean {
    if (
      (Array.isArray(recipient.address) &&
        recipient.address.every(token => typeof token === 'string' && token.length > 0)) ||
      (typeof recipient.address === 'string' && recipient.address.length > 0)
    ) {
      return true;
    }
    return false;
  }
}
