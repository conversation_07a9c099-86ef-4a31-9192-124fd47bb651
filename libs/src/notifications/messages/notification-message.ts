import Handlebars from 'handlebars';
import {
  type INotificationMessage,
  NotificationChannel,
  type NotificationPayload,
  type NotificationQueueMessage,
  type NotificationRecipient,
  type NotificationResult,
} from '../../shared';
import type { INotificationData } from '../../shared/interfaces/notification-data.interface';

export abstract class NotificationMessage<
  TPayload extends NotificationPayload = NotificationPayload
> implements INotificationMessage
{
  readonly id: string;
  readonly subject: string;
  readonly content: string;
  readonly templateData: Record<string, any>;
  readonly payload: TPayload;
  readonly appCode: string;
  readonly eventCode: string;
  readonly recipient: NotificationRecipient;
  readonly notificationChannel: NotificationChannel;
  readonly authorId: string;
  readonly categoryCode?: string;
  constructor(message: NotificationQueueMessage) {
    const { id, payload, appCode, eventCode, channel, recipient, authorId } = message;
    this.payload = this.getChannelSpecificPayload(payload);
    this.id = id;
    this.templateData = payload.templateData ?? {};
    this.subject = this.renderTemplate(payload.subject, this.templateData);
    this.content = this.renderTemplate(payload.content, this.templateData);
    this.notificationChannel = channel;
    this.recipient = recipient;
    this.eventCode = eventCode;
    this.appCode = appCode;
    this.authorId = authorId;
    this.categoryCode = message.categoryCode;
  }
  getChannel(): NotificationChannel {
    return this.notificationChannel;
  }

  getNotificationData(result: NotificationResult): INotificationData {
    return {
      channel: this.notificationChannel,
      recipientId: this.recipient.id,
      content: this.content,
      subject: this.subject,
      id: result.id,
      authorId: this.getAuthorId(),
      deliveredAt: result.deliveredAt,
      appCode: this.appCode,
      eventCode: this.eventCode,
      categoryCode: this.categoryCode,
      isRead: false,
      data: this.templateData,
    };
  }
  getAppCode(): string {
    return this.appCode;
  }

  getEventCode(): string {
    return this.eventCode;
  }

  getAuthorId(): string | undefined {
    return this.authorId;
  }

  abstract readonly channel: NotificationChannel;

  abstract validateRecipient(recipient: NotificationRecipient): boolean;
  abstract getChannelSpecificPayload(payload: NotificationPayload): TPayload;

  getSubject(): string {
    return this.subject;
  }

  getContent(): string {
    return this.content;
  }

  getData(): Record<string, any> {
    return this.templateData || {};
  }

  getRecipient(): NotificationRecipient {
    return this.recipient;
  }

  validate(): boolean {
    if (!this.payload) {
      return false;
    }
    return this.validateRecipient(this.recipient);
  }

  private renderTemplate(template: string, data: Record<string, any>): string {
    try {
      const compiledTemplate = Handlebars.compile(template);
      return compiledTemplate(data);
    } catch (error) {
      return template;
    }
  }
}
