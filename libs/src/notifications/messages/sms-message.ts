import {
  NotificationChannel,
  type NotificationPayload,
  type NotificationRecipient,
} from '../../shared';
import { NotificationMessage } from './notification-message';

export class SmsMessage extends NotificationMessage {
  getChannelSpecificPayload(): NotificationPayload {
    throw new Error('Method not implemented.');
  }

  getRecipient() {
    return { address: '', id: '', metadata: {} };
  }
  channel: NotificationChannel;
  validateRecipient(recipient: NotificationRecipient): boolean {
    throw new Error('Method not implemented.');
  }
}
