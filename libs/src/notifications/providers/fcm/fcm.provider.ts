import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as admin from 'firebase-admin';
import { NotificationChannel, NotificationStatus, type NotificationResult } from '../../../shared';
import { NotificationProvider } from '../notification.provider';
import { FcmMessage } from '../../messages/fcm-message';

@Injectable()
export class FcmProvider extends NotificationProvider<FcmMessage> {
  readonly channel = NotificationChannel.Fcm;
  private app: admin.app.App;

  constructor(private configService: ConfigService) {
    super();
    this.initializeFirebase();
  }

  private initializeFirebase(): void {
    try {
      const projectId = this.configService.get<string>('FCM_PROJECT_ID');
      const privateKey = this.configService.get<string>('FCM_PRIVATE_KEY')?.replace(/\\n/g, '\n');
      const clientEmail = this.configService.get<string>('FCM_CLIENT_EMAIL');
      const appName = this.configService.get<string>('FCM_APP_NAME', 'fcm-app');

      if (!projectId || !privateKey || !clientEmail) {
        this.logger.warn('FCM configuration is incomplete. FCM provider will be disabled.');
        return;
      }

      this.app = admin.initializeApp(
        {
          credential: admin.credential.cert({
            projectId,
            privateKey,
            clientEmail,
          }),
        },
        appName
      );

      this.logger.log('Firebase Admin SDK initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Firebase Admin SDK', error);
    }
  }

  async handle(message: FcmMessage): Promise<NotificationResult> {
    const recipient = message.getRecipient();
    const recipientTokens = message.getRecipientTokens();
    if (!this.app) {
      throw new Error('Firebase Admin SDK not initialized');
    }

    const multicastMessage: admin.messaging.MulticastMessage = {
      tokens: recipientTokens,
      notification: {
        title: message.subject,
        body: message.content,
      },
      ...message.payload,
    };

    const response = await admin.messaging(this.app).sendEachForMulticast(multicastMessage);

    this.logger.log(`FCM notification sent successfully: ${response}`);

    return this.createResult(message.id, recipient.id, NotificationStatus.Delivered, {
      messageId: response,
      fcmToken: recipient.address,
    });
  }

  isConfigured(): boolean {
    return !!this.app;
  }

  async isHealthy(): Promise<boolean> {
    try {
      if (!this.app) {
        return false;
      }
      // Try to get the app instance to verify connection
      const appName = this.configService.get<string>('FCM_APP_NAME', 'fcm-app');
      const app = admin.app(appName);
      return !!app;
    } catch (error) {
      this.logger.error('FCM health check failed', error);
      return false;
    }
  }
}
