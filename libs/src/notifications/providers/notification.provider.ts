import { Injectable, Logger } from '@nestjs/common';
import { NotificationChannel } from '../../shared/enums';
import { NotificationStatus } from '../../shared/enums/notification-status.enum';
import type { INotificationProvider } from '../../shared/interfaces/notification-provider.interface';
import type { INotificationMessage, NotificationResult } from '../../shared';

@Injectable()
export abstract class NotificationProvider<T extends INotificationMessage>
  implements INotificationProvider<T>
{
  protected readonly logger = new Logger(this.constructor.name);

  abstract readonly channel: NotificationChannel;

  send(message: T): Promise<NotificationResult> {
    if (!this.validate(message))
      return this.handleError(new Error('Invalid payload'), message.getRecipient().id, message.id);
    try {
      return this.handle(message);
    } catch (error) {
      return this.handleError(error, message.getRecipient().id, message.id);
    }
  }

  abstract handle(message: T): Promise<NotificationResult>;

  abstract isHealthy(): Promise<boolean>;

  protected createResult(
    id: string,
    recipientId: string,
    status: NotificationStatus,
    metadata?: Record<string, any>,
    failureReason?: string
  ): NotificationResult {
    return {
      id,
      recipientId,
      channel: this.channel,
      status,
      deliveredAt: status === NotificationStatus.Delivered ? new Date() : undefined,
      failureReason,
      metadata,
    };
  }

  protected async handleError(
    error: any,
    recipientId: string,
    id: string
  ): Promise<NotificationResult> {
    this.logger.error(`Failed to send notification via ${this.channel}`, error);
    return this.createResult(
      id,
      recipientId,
      NotificationStatus.Failed,
      { error: error.message },
      error.message
    );
  }

  private validatePayload(payload: T): boolean {
    return true;
  }

  protected validate(payload: T): boolean {
    return payload.validate() && payload.channel === this.channel && this.validatePayload(payload);
  }
}
