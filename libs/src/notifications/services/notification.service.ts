import { Inject, Injectable, Logger } from '@nestjs/common';
import { NotificationStatus } from '../../shared/enums';
import {
  INotificationProvider,
  type INotificationMessage,
  type NotificationResult,
} from '../../shared/interfaces';
import { NotificationRepository, NotificationSnapshotRepository } from '../../database';

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);

  constructor(
    @Inject(INotificationProvider)
    private readonly provider: INotificationProvider<any>,
    private readonly notificationSnapshotRepository: NotificationSnapshotRepository,
    private readonly notificationRepository: NotificationRepository
  ) {}

  async sendNotification(payload: INotificationMessage): Promise<NotificationResult> {
    const result = await this.provider.send(payload);
    if (result.status === NotificationStatus.Failed) {
      await this.notificationSnapshotRepository.update(result.id, {
        status: result.status,
        failureReason: result.failureReason,
        retryCount: 1,
      });
    } else if (result.status === NotificationStatus.Delivered) {
      await this.notificationSnapshotRepository.update(result.id, {
        status: result.status,
        deliveredAt: result.deliveredAt,
      });
      await this.createNotification(payload, result);
    }
    return result;
  }

  private async createNotification(payload: INotificationMessage, result: NotificationResult) {
    const notificationData = payload.getNotificationData(result);
    await this.notificationRepository.create(notificationData);
  }

  async getHealthStatus(): Promise<boolean> {
    try {
      const isHealthy = await this.provider.isHealthy();
      return isHealthy;
    } catch (error) {
      this.logger.error(`Health check failed for ${this.provider.channel}`, error);
    }
    return false;
  }
}
