import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { IQueueService } from './interfaces/queue-service.interface';
import { QueueRouterService } from './services/queue-router.service';

@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        name: IQueueService,
        imports: [ConfigModule],
        useFactory: async (configService: ConfigService) => ({
          transport: Transport.RMQ,
          options: {
            urls: [configService.get<string>('RABBITMQ_URL', 'amqp://localhost:5672')],
            exchange: 'notifications',
            exchangeType: 'topic',
            wildcards: true,
          },
        }),
        inject: [ConfigService],
      },
    ]),
  ],
  providers: [QueueRouterService],
  exports: [ClientsModule, QueueRouterService],
})
export class QueueModule {}
