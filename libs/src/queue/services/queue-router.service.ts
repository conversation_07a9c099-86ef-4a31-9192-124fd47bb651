import { Inject, Injectable, Logger } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import {
  NotificationChannel,
  NotificationPriority,
  type NotificationQueueMessage,
} from '../../shared';
import { IQueueService } from '../interfaces/queue-service.interface';
import { NOTIFICATION_PREFIX_PATTERN } from '../../shared/constants';

@Injectable()
export class QueueRouterService {
  private readonly logger = new Logger(QueueRouterService.name);
  constructor(@Inject(IQueueService) private readonly queueService: ClientProxy) {}

  async routeNotifications(messages: NotificationQueueMessage[]): Promise<void> {
    for (const message of messages) {
      await this.sendToChannelQueue(message);
    }
  }

  private async sendToChannelQueue(message: NotificationQueueMessage): Promise<void> {
    try {
      const pattern = this.getPatternForChannel(message.channel, message.priority);
      this.queueService.emit(pattern, message);
      this.logger.log(`Message routed to ${message.channel} queue: ${message.id}`);
    } catch (error) {
      this.logger.error(`Failed to route message to ${message.channel} queue: ${error.message}`);
    }
  }

  private getPatternForChannel(
    channel: NotificationChannel,
    priority?: NotificationPriority
  ): string {
    return `${NOTIFICATION_PREFIX_PATTERN}.${channel.toLowerCase()}.${(
      priority ?? NotificationPriority.Normal
    ).toLowerCase()}`;
  }

  getSupportedChannels(): NotificationChannel[] {
    return [
      NotificationChannel.Fcm,
      NotificationChannel.Apns,
      NotificationChannel.Email,
      NotificationChannel.Sms,
      NotificationChannel.Wns,
    ];
  }

  isChannelSupported(channel: string): boolean {
    return this.getSupportedChannels().includes(channel as NotificationChannel);
  }
}
