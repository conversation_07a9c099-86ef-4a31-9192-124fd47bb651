import { NotificationChannel } from '../enums';
import type { INotificationData } from './notification-data.interface';
import type { NotificationRecipient } from './notification-recipient.interface';
import type { NotificationResult } from './notification-result.interface';

export interface INotificationMessage {
  readonly id: string;
  readonly channel: NotificationChannel;

  getSubject(): string;

  getContent(): string;

  getData(): Record<string, any>;

  getRecipient(): NotificationRecipient;

  getAuthorId(): string | undefined;

  getAppCode(): string;

  getEventCode(): string;

  getChannel(): NotificationChannel;
  validate(): boolean;

  getNotificationData(result: NotificationResult): INotificationData;
}
