import type { EmailProviderType } from '../enums';
import * as admin from 'firebase-admin';

export interface NotificationPayload {
  subject: string;
  content: string;
  templateData?: Record<string, any>;
  [key: string]: any;
}

export interface EmailPayload extends NotificationPayload {
  attachments?: AttachmentPayload[];
  to?: string[];
  cc?: string[];
  bcc?: string[];
  provider: EmailProviderType;
}

type FcmMessage = Pick<
  admin.messaging.MulticastMessage,
  'android' | 'webpush' | 'apns' | 'fcmOptions'
>;
export interface FcmPayload extends NotificationPayload, FcmMessage {
  data?: Record<string, any>;
}

export class AttachmentPayload {
  filename: string;
  url: string;
}
