import { NotificationChannel } from '../enums';
import type { INotificationMessage } from './notification-message.interface';
import type { NotificationResult } from './notification-result.interface';

export interface INotificationProvider<T extends INotificationMessage> {
  readonly channel: NotificationChannel;
  send(message: T): Promise<NotificationResult>;
  isHealthy(): Promise<boolean>;
}

export const INotificationProvider = Symbol('INotificationProvider');
