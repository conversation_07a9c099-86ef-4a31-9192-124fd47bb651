import type { NotificationChannel, NotificationPriority } from '../enums';
import type { NotificationPayload } from './notification-payload.interface';
import type { NotificationRecipient } from './notification-recipient.interface';

export interface NotificationQueueMessage {
  id: string;
  templateCode?: string;
  payload: NotificationPayload;
  recipient: NotificationRecipient;
  channel: NotificationChannel;
  priority: NotificationPriority;
  fallbackChannels: NotificationChannel[];
  authorId?: string;
  eventCode: string;
  categoryCode: string;
  appCode: string;
}
