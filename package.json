{"name": "herond-notification-center", "version": "1.0.0", "description": "A scalable notification center solution supporting multiple platforms and channels", "license": "MIT", "scripts": {"build": "nx run-many --target=build", "test": "nx run-many --target=test", "test:e2e": "nx e2e", "lint": "nx run-many --target=lint", "format": "nx format:write", "format:check": "nx format:check", "build:libs": "nx build libs", "start:all": "nx run-many --target=serve --all", "start": "nx run-many --target=serve", "prepare": "husky install", "db:generate": "rm -rf ./prisma/generated && prisma format && prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "rm -rf ./prisma/generated && prisma studio --port 8000", "db:seed": "tsx prisma/seed.ts"}, "private": true, "dependencies": {"@keyv/redis": "^5.1.2", "@nestjs/axios": "^4.0.1", "@nestjs/common": "^11.0.0", "@nestjs/config": "^4.0.0", "@nestjs/core": "^11.0.0", "@nestjs/cqrs": "^11.0.3", "@nestjs/microservices": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.0", "@nestjs/schedule": "^5.0.0", "@nestjs/swagger": "^11.0.0", "@nestjs/terminus": "^11.0.0", "@nestjs/throttler": "^6.4.0", "@prisma/client": "^6.1.0", "amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.9", "apn": "^2.2.0", "axios": "^1.12.2", "bcrypt": "^5.1.0", "bson": "^6.10.4", "cache-manager": "^7.2.3", "class-transformer": "^0.5.0", "class-validator": "^0.14.0", "firebase-admin": "^12.0.0", "handlebars": "^4.7.8", "jsonwebtoken": "^9.0.0", "jwks-rsa": "^3.2.0", "keyv": "^5.5.3", "nestjs-cls": "^6.0.1", "nodemailer": "^6.9.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "prisma": "^6.1.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0", "twilio": "^5.0.0", "uuid": "^9.0.0"}, "engines": {"node": ">=22.0.0", "npm": ">=10.0.0"}, "devDependencies": {"@commitlint/cli": "^18.0.0", "@commitlint/config-conventional": "^18.0.0", "@eslint/js": "^9.8.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.0", "@nx/eslint": "21.5.1", "@nx/eslint-plugin": "21.5.1", "@nx/jest": "21.5.1", "@nx/js": "21.5.1", "@nx/nest": "21.5.1", "@nx/node": "21.5.1", "@nx/web": "21.5.1", "@nx/webpack": "21.5.1", "@nx/workspace": "21.5.1", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/amqplib": "^0.10.0", "@types/bcrypt": "^5.0.0", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.0", "@types/node": "^22.10.0", "@types/nodemailer": "^6.4.0", "@types/uuid": "^9.0.0", "eslint": "^9.8.0", "eslint-config-prettier": "^10.0.0", "husky": "^8.0.0", "jest": "^30.0.2", "jest-environment-node": "^30.0.2", "jest-util": "^30.0.2", "lint-staged": "^15.0.0", "nx": "21.5.1", "prettier": "^2.6.2", "supertest": "^6.3.0", "ts-jest": "^29.4.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "tsx": "^4.19.0", "typescript": "~5.9.2", "typescript-eslint": "^8.40.0", "webpack-cli": "^5.1.4"}}