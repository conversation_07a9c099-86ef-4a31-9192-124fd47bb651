model Device {
  id               String    @id @map("_id") @db.ObjectId
  userId           String
  appCode          String
  deviceToken      String
  platform         String
  osVersion        String
  appVersion       String
  tokenRefreshedAt DateTime?
  expiredAt        DateTime?
  invalidReason    String?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt

  @@map("devices")
}
