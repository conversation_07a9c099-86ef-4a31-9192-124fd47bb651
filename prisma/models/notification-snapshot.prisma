model NotificationMessageSnapshot {
  id               String                @id @map("_id") @db.ObjectId
  templateCode     String?
  payload          Json
  recipient        <PERSON>son
  channel          NotificationChannel
  priority         NotificationPriority
  fallbackChannels NotificationChannel[]
  authorId         String?

  status        NotificationStatus
  deliveredAt   DateTime?
  failureReason String?
  metadata      Json?
  retryCount    Int
  maxRetries    Int
  createdAt     DateTime
  scheduledAt   DateTime?
  expiresAt     DateTime?

  @@map("notification_message_snapshots")
}
