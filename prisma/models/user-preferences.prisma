model UserPreferences {
  id                 String   @id @default(auto()) @map("_id") @db.ObjectId
  userId             String
  appCode            String
  allowNotifications Boolean  @default(true)
  scopes             Scope[]
  options            Option[]
  updatedAt          DateTime @updatedAt

  @@map("user_preferences")
}

type Scope {
  field String
  value String
}

type Option {
  category String
  channels NotificationChannel[]
  enabled  Boolean
}
