import { MongoMemoryServer } from 'mongodb-memory-server';
import { ConfigService } from '@nestjs/config';

// Global test setup
let mongod: MongoMemoryServer;

beforeAll(async () => {
  // Start in-memory MongoDB instance for testing
  mongod = await MongoMemoryServer.create();
  const uri = mongod.getUri();
  process.env.DATABASE_URL = uri;
  process.env.NODE_ENV = 'test';

  // Set test environment variables
  process.env.RABBITMQ_URL = 'amqp://localhost:5672';
  process.env.JWT_SECRET = 'test-secret';
  process.env.LOG_LEVEL = 'error';
});

afterAll(async () => {
  if (mongod) {
    await mongod.stop();
  }
});

// Mock external services for testing
jest.mock('firebase-admin', () => ({
  initializeApp: jest.fn(),
  credential: {
    cert: jest.fn(),
  },
  messaging: jest.fn(() => ({
    send: jest.fn().mockResolvedValue('mock-message-id'),
  })),
  app: jest.fn(),
}));

jest.mock('apn', () => ({
  Provider: jest.fn().mockImplementation(() => ({
    send: jest.fn().mockResolvedValue({ sent: [{}], failed: [] }),
    shutdown: jest.fn(),
  })),
  Notification: jest.fn().mockImplementation(() => ({
    topic: '',
    alert: {},
    badge: 0,
    sound: '',
    payload: {},
    mutableContent: false,
  })),
}));

jest.mock('nodemailer', () => ({
  createTransport: jest.fn(() => ({
    sendMail: jest.fn().mockResolvedValue({
      messageId: 'mock-message-id',
      response: 'mock-response',
    }),
    verify: jest.fn().mockResolvedValue(true),
    close: jest.fn(),
  })),
}));

jest.mock('twilio', () => ({
  Twilio: jest.fn().mockImplementation(() => ({
    messages: {
      create: jest.fn().mockResolvedValue({
        sid: 'mock-message-sid',
        status: 'sent',
        direction: 'outbound-api',
      }),
    },
    api: {
      accounts: jest.fn(() => ({
        fetch: jest.fn().mockResolvedValue({}),
      })),
    },
    accountSid: 'mock-account-sid',
  })),
}));
